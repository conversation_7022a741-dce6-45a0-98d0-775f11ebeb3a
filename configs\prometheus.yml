# Prometheus configuration for Edge Device Fleet Manager

global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Edge Fleet Manager application
  - job_name: 'edge-fleet-manager'
    static_configs:
      - targets: ['edge-fleet-manager:9090']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # Redis metrics (if redis_exporter is used)
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    metrics_path: '/metrics'

  # PostgreSQL metrics (if postgres_exporter is used)
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
    metrics_path: '/metrics'

  # MQTT broker metrics (if mosquitto_exporter is used)
  - job_name: 'mosquitto'
    static_configs:
      - targets: ['mosquitto:1883']
    metrics_path: '/metrics'

<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">34%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-07-11 19:01 +0530
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_73bda9688a204f7a___init___py.html">edge_device_fleet_manager\__init__.py</a></td>
                <td class="name left"><a href="z_73bda9688a204f7a___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c___init___py.html">edge_device_fleet_manager\cli\__init__.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html#t29">edge_device_fleet_manager\cli\main.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html#t29"><data value='invoke'>AsyncGroup.invoke</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html#t62">edge_device_fleet_manager\cli\main.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html#t62"><data value='cli'>cli</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html#t133">edge_device_fleet_manager\cli\main.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html#t133"><data value='config'>config</data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html#t177">edge_device_fleet_manager\cli\main.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html#t177"><data value='plugins'>plugins</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html#t219">edge_device_fleet_manager\cli\main.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html#t219"><data value='reload_plugin'>reload_plugin</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html#t252">edge_device_fleet_manager\cli\main.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html#t252"><data value='debug_repl'>debug_repl</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html#t280">edge_device_fleet_manager\cli\main.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html#t280"><data value='main'>main</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html">edge_device_fleet_manager\cli\main.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>38</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="38 38">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t25">edge_device_fleet_manager\cli\types.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t25"><data value='init__'>CachedSchema.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t30">edge_device_fleet_manager\cli\types.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t30"><data value='is_expired'>CachedSchema.is_expired</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t34">edge_device_fleet_manager\cli\types.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t34"><data value='validate'>CachedSchema.validate</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t47">edge_device_fleet_manager\cli\types.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t47"><data value='init__'>DeviceIDType.__init__</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t68">edge_device_fleet_manager\cli\types.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t68"><data value='convert'>DeviceIDType.convert</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t118">edge_device_fleet_manager\cli\types.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t118"><data value='validate_with_schema'>DeviceIDType._validate_with_schema</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t124">edge_device_fleet_manager\cli\types.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t124"><data value='get_schema'>DeviceIDType._get_schema</data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t181">edge_device_fleet_manager\cli\types.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t181"><data value='shell_complete'>DeviceIDType.shell_complete</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t210">edge_device_fleet_manager\cli\types.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t210"><data value='get_device_ids'>DeviceIDType._get_device_ids</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t253">edge_device_fleet_manager\cli\types.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t253"><data value='init__'>IPAddressType.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t256">edge_device_fleet_manager\cli\types.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t256"><data value='convert'>IPAddressType.convert</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t294">edge_device_fleet_manager\cli\types.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t294"><data value='convert'>SubnetType.convert</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html">edge_device_fleet_manager\cli\types.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t130">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t130"><data value='validate_logging_level'>Config.validate_logging_level</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t139">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t139"><data value='validate_discovery_config'>Config.validate_discovery_config</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t153">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t153"><data value='init__'>SecretsManager.__init__</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t161">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t161"><data value='client'>SecretsManager.client</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t170">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t170"><data value='get_encryption_key'>SecretsManager.get_encryption_key</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t198">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t198"><data value='store_encryption_key'>SecretsManager._store_encryption_key</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t223">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t223"><data value='get_secret'>SecretsManager.get_secret</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t259">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t259"><data value='set_secret'>SecretsManager.set_secret</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t315">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t315"><data value='check_rotation_needed'>SecretsManager.check_rotation_needed</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t344">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t344"><data value='rotate_encryption_key'>SecretsManager.rotate_encryption_key</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t401">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t401"><data value='init__'>ConfigLoader.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t405">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t405"><data value='load_config'>ConfigLoader.load_config</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t424">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t424"><data value='load_yaml_config'>ConfigLoader._load_yaml_config</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t447">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t447"><data value='load_env_config'>ConfigLoader._load_env_config</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t453">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t453"><data value='load_secrets'>ConfigLoader._load_secrets</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t479">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t479"><data value='set_nested_config_value'>ConfigLoader._set_nested_config_value</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t495">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t495"><data value='get_config'>get_config</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t507">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t507"><data value='get_config_sync'>get_config_sync</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>112</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="112 112">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t33">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t33"><data value='init__'>AppContext.__init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t37">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t37"><data value='config'>AppContext.config</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t42">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t42"><data value='config'>AppContext.config</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t47">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t47"><data value='correlation_id'>AppContext.correlation_id</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t52">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t52"><data value='correlation_id'>AppContext.correlation_id</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t57">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t57"><data value='db_session'>AppContext.db_session</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t62">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t62"><data value='db_session'>AppContext.db_session</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t67">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t67"><data value='mqtt_client'>AppContext.mqtt_client</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t72">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t72"><data value='mqtt_client'>AppContext.mqtt_client</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t77">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t77"><data value='redis_client'>AppContext.redis_client</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t82">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t82"><data value='redis_client'>AppContext.redis_client</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t87">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t87"><data value='user'>AppContext.user</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t92">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t92"><data value='user'>AppContext.user</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t97">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t97"><data value='request'>AppContext.request</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t102">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t102"><data value='request'>AppContext.request</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t106">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t106"><data value='generate_correlation_id'>AppContext.generate_correlation_id</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t112">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t112"><data value='get_context_data'>AppContext.get_context_data</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t124">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t124"><data value='clear'>AppContext.clear</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t140">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t140"><data value='context_manager'>context_manager</data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t192">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t192"><data value='async_context_manager'>async_context_manager</data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t243">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t243"><data value='run_in_context'>run_in_context</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t249">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t249"><data value='run_in_context_async'>run_in_context_async</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t256">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t256"><data value='get_current_context'>get_current_context</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t261">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t261"><data value='require_config'>require_config</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t272">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t272"><data value='require_correlation_id'>require_correlation_id</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t283">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t283"><data value='require_db_session'>require_db_session</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>61</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="61 61">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t11">edge_device_fleet_manager\core\exceptions.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t11"><data value='init__'>EdgeFleetError.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html">edge_device_fleet_manager\core\exceptions.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>31</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="31 31">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t28">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t28"><data value='call__'>CorrelationIDProcessor.__call__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t38">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t38"><data value='init__'>SamplingProcessor.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t41">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t41"><data value='call__'>SamplingProcessor.__call__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t53">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t53"><data value='call__'>TimestampProcessor.__call__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t61">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t61"><data value='call__'>LevelProcessor.__call__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t69">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t69"><data value='call__'>ContextProcessor.__call__</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t95">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t95"><data value='call__'>ExceptionProcessor.__call__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t113">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t113"><data value='init__'>AsyncSentryHandler.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t119">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t119"><data value='start'>AsyncSentryHandler.start</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t125">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t125"><data value='stop'>AsyncSentryHandler.stop</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t131">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t131"><data value='emit'>AsyncSentryHandler.emit</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t140">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t140"><data value='process_queue'>AsyncSentryHandler._process_queue</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t152">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t152"><data value='send_to_sentry'>AsyncSentryHandler._send_to_sentry</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t191">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t191"><data value='setup_logging'>setup_logging</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t259">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t259"><data value='get_logger'>get_logger</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t264">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t264"><data value='log_exception'>log_exception</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t280">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t280"><data value='log_performance'>log_performance</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t295">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t295"><data value='log_audit'>log_audit</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>42</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="42 42">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t29">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t29"><data value='init__'>PluginMetadata.__init__</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t51">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t51"><data value='init__'>Plugin.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t58">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t58"><data value='initialize'>Plugin.initialize</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t62">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t62"><data value='cleanup'>Plugin.cleanup</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t66">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t66"><data value='get_commands'>Plugin.get_commands</data></a></td>
                <td>5</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="4 5">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t81">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t81"><data value='init__'>PluginLoadResult.__init__</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t97">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t97"><data value='init__'>PluginFileHandler.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t101">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t101"><data value='on_modified'>PluginFileHandler.on_modified</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t125">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t125"><data value='on_created'>PluginFileHandler.on_created</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t141">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t141"><data value='on_deleted'>PluginFileHandler.on_deleted</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t161">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t161"><data value='init__'>PluginLoader.__init__</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t173">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t173"><data value='set_cli_group'>PluginLoader.set_cli_group</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t177">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t177"><data value='start'>PluginLoader.start</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t188">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t188"><data value='stop'>PluginLoader.stop</data></a></td>
                <td>6</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="3 6">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t201">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t201"><data value='start_file_watcher'>PluginLoader.start_file_watcher</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t213">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t213"><data value='load_all_plugins'>PluginLoader.load_all_plugins</data></a></td>
                <td>8</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="7 8">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t233">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t233"><data value='load_plugin_from_file'>PluginLoader.load_plugin_from_file</data></a></td>
                <td>39</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="31 39">79%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t333">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t333"><data value='unload_plugin'>PluginLoader.unload_plugin</data></a></td>
                <td>20</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="12 20">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t371">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t371"><data value='unload_all_plugins'>PluginLoader.unload_all_plugins</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t377">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t377"><data value='reload_plugin_from_file'>PluginLoader.reload_plugin_from_file</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t385">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t385"><data value='unload_plugin_from_file'>PluginLoader.unload_plugin_from_file</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t395">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t395"><data value='get_loaded_plugins'>PluginLoader.get_loaded_plugins</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t399">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t399"><data value='get_plugin'>PluginLoader.get_plugin</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t403">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t403"><data value='is_plugin_loaded'>PluginLoader.is_plugin_loaded</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t412">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t412"><data value='get_plugin_loader'>get_plugin_loader</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t417">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t417"><data value='set_plugin_loader'>set_plugin_loader</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t423">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t423"><data value='initialize_plugin_system'>initialize_plugin_system</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t432">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t432"><data value='shutdown_plugin_system'>shutdown_plugin_system</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>51</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="51 51">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_50d91cd27b284a0f___init___py.html">edge_device_fleet_manager\plugins\__init__.py</a></td>
                <td class="name left"><a href="z_50d91cd27b284a0f___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_50d91cd27b284a0f_sample_plugin_py.html#t25">edge_device_fleet_manager\plugins\sample_plugin.py</a></td>
                <td class="name left"><a href="z_50d91cd27b284a0f_sample_plugin_py.html#t25"><data value='initialize'>SamplePlugin.initialize</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_50d91cd27b284a0f_sample_plugin_py.html#t29">edge_device_fleet_manager\plugins\sample_plugin.py</a></td>
                <td class="name left"><a href="z_50d91cd27b284a0f_sample_plugin_py.html#t29"><data value='cleanup'>SamplePlugin.cleanup</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_50d91cd27b284a0f_sample_plugin_py.html#t35">edge_device_fleet_manager\plugins\sample_plugin.py</a></td>
                <td class="name left"><a href="z_50d91cd27b284a0f_sample_plugin_py.html#t35"><data value='hello'>SamplePlugin.hello</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_50d91cd27b284a0f_sample_plugin_py.html#t42">edge_device_fleet_manager\plugins\sample_plugin.py</a></td>
                <td class="name left"><a href="z_50d91cd27b284a0f_sample_plugin_py.html#t42"><data value='status'>SamplePlugin.status</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_50d91cd27b284a0f_sample_plugin_py.html">edge_device_fleet_manager\plugins\sample_plugin.py</a></td>
                <td class="name left"><a href="z_50d91cd27b284a0f_sample_plugin_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4e142099aadd2379___init___py.html">edge_device_fleet_manager\utils\__init__.py</a></td>
                <td class="name left"><a href="z_4e142099aadd2379___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t18">edge_device_fleet_manager\utils\decorators.py</a></td>
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t18"><data value='with_correlation_id'>with_correlation_id</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t22">edge_device_fleet_manager\utils\decorators.py</a></td>
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t22"><data value='wrapper'>with_correlation_id.wrapper</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t28">edge_device_fleet_manager\utils\decorators.py</a></td>
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t28"><data value='async_wrapper'>with_correlation_id.async_wrapper</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t39">edge_device_fleet_manager\utils\decorators.py</a></td>
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t39"><data value='log_execution_time'>log_execution_time</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t42">edge_device_fleet_manager\utils\decorators.py</a></td>
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t42"><data value='decorator'>log_execution_time.decorator</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t46">edge_device_fleet_manager\utils\decorators.py</a></td>
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t46"><data value='wrapper'>log_execution_time.decorator.wrapper</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t71">edge_device_fleet_manager\utils\decorators.py</a></td>
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t71"><data value='async_wrapper'>log_execution_time.decorator.async_wrapper</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t103">edge_device_fleet_manager\utils\decorators.py</a></td>
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t103"><data value='retry'>retry</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t111">edge_device_fleet_manager\utils\decorators.py</a></td>
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t111"><data value='decorator'>retry.decorator</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t113">edge_device_fleet_manager\utils\decorators.py</a></td>
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t113"><data value='wrapper'>retry.decorator.wrapper</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t144">edge_device_fleet_manager\utils\decorators.py</a></td>
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t144"><data value='async_wrapper'>retry.decorator.async_wrapper</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t182">edge_device_fleet_manager\utils\decorators.py</a></td>
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t182"><data value='validate_config'>validate_config</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t186">edge_device_fleet_manager\utils\decorators.py</a></td>
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t186"><data value='wrapper'>validate_config.wrapper</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t195">edge_device_fleet_manager\utils\decorators.py</a></td>
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t195"><data value='async_wrapper'>validate_config.async_wrapper</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t209">edge_device_fleet_manager\utils\decorators.py</a></td>
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t209"><data value='handle_exceptions'>handle_exceptions</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t216">edge_device_fleet_manager\utils\decorators.py</a></td>
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t216"><data value='decorator'>handle_exceptions.decorator</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t218">edge_device_fleet_manager\utils\decorators.py</a></td>
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t218"><data value='wrapper'>handle_exceptions.decorator.wrapper</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t235">edge_device_fleet_manager\utils\decorators.py</a></td>
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t235"><data value='async_wrapper'>handle_exceptions.decorator.async_wrapper</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html">edge_device_fleet_manager\utils\decorators.py</a></td>
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>1270</td>
                <td>837</td>
                <td>2</td>
                <td class="right" data-ratio="433 1270">34%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-07-11 19:01 +0530
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>

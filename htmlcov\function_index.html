<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">37%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-07-16 17:46 +0530
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_73bda9688a204f7a___init___py.html">edge_device_fleet_manager\__init__.py</a></td>
                <td class="name left"><a href="z_73bda9688a204f7a___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c___init___py.html">edge_device_fleet_manager\cli\__init__.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html#t32">edge_device_fleet_manager\cli\main.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html#t32"><data value='invoke'>AsyncGroup.invoke</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html#t40">edge_device_fleet_manager\cli\main.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html#t40"><data value='invoke'>AsyncCommand.invoke</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html#t73">edge_device_fleet_manager\cli\main.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html#t73"><data value='cli'>cli</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html#t124">edge_device_fleet_manager\cli\main.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html#t124"><data value='config'>config</data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html#t168">edge_device_fleet_manager\cli\main.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html#t168"><data value='plugins'>plugins</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html#t210">edge_device_fleet_manager\cli\main.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html#t210"><data value='reload_plugin'>reload_plugin</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html#t249">edge_device_fleet_manager\cli\main.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html#t249"><data value='discover'>discover</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html#t293">edge_device_fleet_manager\cli\main.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html#t293"><data value='devices'>devices</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html#t333">edge_device_fleet_manager\cli\main.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html#t333"><data value='debug_repl'>debug_repl</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html#t361">edge_device_fleet_manager\cli\main.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html#t361"><data value='main'>main</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html">edge_device_fleet_manager\cli\main.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>58</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="58 58">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t25">edge_device_fleet_manager\cli\types.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t25"><data value='init__'>CachedSchema.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t30">edge_device_fleet_manager\cli\types.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t30"><data value='is_expired'>CachedSchema.is_expired</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t34">edge_device_fleet_manager\cli\types.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t34"><data value='validate'>CachedSchema.validate</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t47">edge_device_fleet_manager\cli\types.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t47"><data value='init__'>DeviceIDType.__init__</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t68">edge_device_fleet_manager\cli\types.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t68"><data value='convert'>DeviceIDType.convert</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t118">edge_device_fleet_manager\cli\types.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t118"><data value='validate_with_schema'>DeviceIDType._validate_with_schema</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t124">edge_device_fleet_manager\cli\types.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t124"><data value='get_schema'>DeviceIDType._get_schema</data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t181">edge_device_fleet_manager\cli\types.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t181"><data value='shell_complete'>DeviceIDType.shell_complete</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t210">edge_device_fleet_manager\cli\types.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t210"><data value='get_device_ids'>DeviceIDType._get_device_ids</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t253">edge_device_fleet_manager\cli\types.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t253"><data value='init__'>IPAddressType.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t256">edge_device_fleet_manager\cli\types.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t256"><data value='convert'>IPAddressType.convert</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t294">edge_device_fleet_manager\cli\types.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t294"><data value='convert'>SubnetType.convert</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html">edge_device_fleet_manager\cli\types.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t130">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t130"><data value='validate_logging_level'>Config.validate_logging_level</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t139">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t139"><data value='validate_discovery_config'>Config.validate_discovery_config</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t153">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t153"><data value='init__'>SecretsManager.__init__</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t161">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t161"><data value='client'>SecretsManager.client</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t170">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t170"><data value='get_encryption_key'>SecretsManager.get_encryption_key</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t197">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t197"><data value='store_encryption_key'>SecretsManager._store_encryption_key</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t220">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t220"><data value='get_secret'>SecretsManager.get_secret</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t255">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t255"><data value='set_secret'>SecretsManager.set_secret</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t308">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t308"><data value='check_rotation_needed'>SecretsManager.check_rotation_needed</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t336">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t336"><data value='rotate_encryption_key'>SecretsManager.rotate_encryption_key</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t391">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t391"><data value='init__'>ConfigLoader.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t395">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t395"><data value='load_config'>ConfigLoader.load_config</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t414">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t414"><data value='load_yaml_config'>ConfigLoader._load_yaml_config</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t437">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t437"><data value='load_env_config'>ConfigLoader._load_env_config</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t443">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t443"><data value='load_secrets'>ConfigLoader._load_secrets</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t469">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t469"><data value='set_nested_config_value'>ConfigLoader._set_nested_config_value</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t485">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t485"><data value='get_config'>get_config</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t497">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t497"><data value='get_config_sync'>get_config_sync</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>112</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="112 112">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t33">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t33"><data value='init__'>AppContext.__init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t37">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t37"><data value='config'>AppContext.config</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t42">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t42"><data value='config'>AppContext.config</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t47">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t47"><data value='correlation_id'>AppContext.correlation_id</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t52">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t52"><data value='correlation_id'>AppContext.correlation_id</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t57">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t57"><data value='db_session'>AppContext.db_session</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t62">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t62"><data value='db_session'>AppContext.db_session</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t67">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t67"><data value='mqtt_client'>AppContext.mqtt_client</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t72">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t72"><data value='mqtt_client'>AppContext.mqtt_client</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t77">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t77"><data value='redis_client'>AppContext.redis_client</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t82">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t82"><data value='redis_client'>AppContext.redis_client</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t87">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t87"><data value='user'>AppContext.user</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t92">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t92"><data value='user'>AppContext.user</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t97">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t97"><data value='request'>AppContext.request</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t102">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t102"><data value='request'>AppContext.request</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t106">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t106"><data value='generate_correlation_id'>AppContext.generate_correlation_id</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t112">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t112"><data value='get_context_data'>AppContext.get_context_data</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t124">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t124"><data value='clear'>AppContext.clear</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t140">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t140"><data value='context_manager'>context_manager</data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t192">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t192"><data value='async_context_manager'>async_context_manager</data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t243">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t243"><data value='run_in_context'>run_in_context</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t249">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t249"><data value='run_in_context_async'>run_in_context_async</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t256">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t256"><data value='get_current_context'>get_current_context</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t261">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t261"><data value='require_config'>require_config</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t272">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t272"><data value='require_correlation_id'>require_correlation_id</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t283">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t283"><data value='require_db_session'>require_db_session</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>61</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="61 61">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t11">edge_device_fleet_manager\core\exceptions.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t11"><data value='init__'>EdgeFleetError.__init__</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html">edge_device_fleet_manager\core\exceptions.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>31</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="31 31">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t28">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t28"><data value='call__'>CorrelationIDProcessor.__call__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t38">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t38"><data value='init__'>SamplingProcessor.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t41">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t41"><data value='call__'>SamplingProcessor.__call__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t53">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t53"><data value='call__'>TimestampProcessor.__call__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t61">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t61"><data value='call__'>LevelProcessor.__call__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t69">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t69"><data value='call__'>ContextProcessor.__call__</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t95">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t95"><data value='call__'>ExceptionProcessor.__call__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t113">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t113"><data value='init__'>AsyncSentryHandler.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t119">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t119"><data value='start'>AsyncSentryHandler.start</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t125">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t125"><data value='stop'>AsyncSentryHandler.stop</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t131">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t131"><data value='emit'>AsyncSentryHandler.emit</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t140">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t140"><data value='process_queue'>AsyncSentryHandler._process_queue</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t152">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t152"><data value='send_to_sentry'>AsyncSentryHandler._send_to_sentry</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t191">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t191"><data value='setup_logging'>setup_logging</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t259">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t259"><data value='get_logger'>get_logger</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t264">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t264"><data value='log_exception'>log_exception</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t280">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t280"><data value='log_performance'>log_performance</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t295">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t295"><data value='log_audit'>log_audit</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>42</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="42 42">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t29">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t29"><data value='init__'>PluginMetadata.__init__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t51">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t51"><data value='init__'>Plugin.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t58">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t58"><data value='initialize'>Plugin.initialize</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t62">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t62"><data value='cleanup'>Plugin.cleanup</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t66">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t66"><data value='get_commands'>Plugin.get_commands</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t81">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t81"><data value='init__'>PluginLoadResult.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t97">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t97"><data value='init__'>PluginFileHandler.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t101">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t101"><data value='on_modified'>PluginFileHandler.on_modified</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t125">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t125"><data value='on_created'>PluginFileHandler.on_created</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t141">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t141"><data value='on_deleted'>PluginFileHandler.on_deleted</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t161">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t161"><data value='init__'>PluginLoader.__init__</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t173">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t173"><data value='set_cli_group'>PluginLoader.set_cli_group</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t177">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t177"><data value='start'>PluginLoader.start</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t188">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t188"><data value='stop'>PluginLoader.stop</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t201">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t201"><data value='start_file_watcher'>PluginLoader.start_file_watcher</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t213">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t213"><data value='load_all_plugins'>PluginLoader.load_all_plugins</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t233">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t233"><data value='load_plugin_from_file'>PluginLoader.load_plugin_from_file</data></a></td>
                <td>39</td>
                <td>39</td>
                <td>0</td>
                <td class="right" data-ratio="0 39">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t333">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t333"><data value='unload_plugin'>PluginLoader.unload_plugin</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t371">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t371"><data value='unload_all_plugins'>PluginLoader.unload_all_plugins</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t377">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t377"><data value='reload_plugin_from_file'>PluginLoader.reload_plugin_from_file</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t385">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t385"><data value='unload_plugin_from_file'>PluginLoader.unload_plugin_from_file</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t395">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t395"><data value='get_loaded_plugins'>PluginLoader.get_loaded_plugins</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t399">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t399"><data value='get_plugin'>PluginLoader.get_plugin</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t403">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t403"><data value='is_plugin_loaded'>PluginLoader.is_plugin_loaded</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t412">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t412"><data value='get_plugin_loader'>get_plugin_loader</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t417">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t417"><data value='set_plugin_loader'>set_plugin_loader</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t423">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t423"><data value='initialize_plugin_system'>initialize_plugin_system</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t432">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t432"><data value='shutdown_plugin_system'>shutdown_plugin_system</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>51</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="51 51">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d___init___py.html">edge_device_fleet_manager\discovery\__init__.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t29">edge_device_fleet_manager\discovery\cache.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t29"><data value='init__'>MemoryCache.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t34">edge_device_fleet_manager\discovery\cache.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t34"><data value='get'>MemoryCache.get</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t44">edge_device_fleet_manager\discovery\cache.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t44"><data value='set'>MemoryCache.set</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t53">edge_device_fleet_manager\discovery\cache.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t53"><data value='delete'>MemoryCache.delete</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t60">edge_device_fleet_manager\discovery\cache.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t60"><data value='exists'>MemoryCache.exists</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t64">edge_device_fleet_manager\discovery\cache.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t64"><data value='clear'>MemoryCache.clear</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t69">edge_device_fleet_manager\discovery\cache.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t69"><data value='keys'>MemoryCache.keys</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t86">edge_device_fleet_manager\discovery\cache.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t86"><data value='init__'>RedisCache.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t91">edge_device_fleet_manager\discovery\cache.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t91"><data value='get'>RedisCache.get</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t100">edge_device_fleet_manager\discovery\cache.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t100"><data value='set'>RedisCache.set</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t110">edge_device_fleet_manager\discovery\cache.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t110"><data value='delete'>RedisCache.delete</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t119">edge_device_fleet_manager\discovery\cache.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t119"><data value='exists'>RedisCache.exists</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t128">edge_device_fleet_manager\discovery\cache.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t128"><data value='clear'>RedisCache.clear</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t137">edge_device_fleet_manager\discovery\cache.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t137"><data value='keys'>RedisCache.keys</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t150">edge_device_fleet_manager\discovery\cache.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t150"><data value='init__'>DiscoveryCache.__init__</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t179">edge_device_fleet_manager\discovery\cache.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t179"><data value='device_key'>DiscoveryCache._device_key</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t183">edge_device_fleet_manager\discovery\cache.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t183"><data value='ip_key'>DiscoveryCache._ip_key</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t187">edge_device_fleet_manager\discovery\cache.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t187"><data value='discovery_key'>DiscoveryCache._discovery_key</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t191">edge_device_fleet_manager\discovery\cache.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t191"><data value='cache_device'>DiscoveryCache.cache_device</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t209">edge_device_fleet_manager\discovery\cache.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t209"><data value='get_device'>DiscoveryCache.get_device</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t224">edge_device_fleet_manager\discovery\cache.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t224"><data value='get_device_by_ip'>DiscoveryCache.get_device_by_ip</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t238">edge_device_fleet_manager\discovery\cache.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t238"><data value='remove_device'>DiscoveryCache.remove_device</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t257">edge_device_fleet_manager\discovery\cache.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t257"><data value='cache_discovery_result'>DiscoveryCache.cache_discovery_result</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t272">edge_device_fleet_manager\discovery\cache.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t272"><data value='get_discovery_result'>DiscoveryCache.get_discovery_result</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t286">edge_device_fleet_manager\discovery\cache.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t286"><data value='clear_all'>DiscoveryCache.clear_all</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t290">edge_device_fleet_manager\discovery\cache.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t290"><data value='get_cached_devices'>DiscoveryCache.get_cached_devices</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t308">edge_device_fleet_manager\discovery\cache.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t308"><data value='dict_to_device'>DiscoveryCache._dict_to_device</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html">edge_device_fleet_manager\discovery\cache.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>43</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="41 43">95%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t76">edge_device_fleet_manager\discovery\core.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t76"><data value='update_last_seen'>Device.update_last_seen</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t81">edge_device_fleet_manager\discovery\core.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t81"><data value='is_stale'>Device.is_stale</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t86">edge_device_fleet_manager\discovery\core.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t86"><data value='to_dict'>Device.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t120">edge_device_fleet_manager\discovery\core.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t120"><data value='add_device'>DiscoveryResult.add_device</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t124">edge_device_fleet_manager\discovery\core.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t124"><data value='get_device_count'>DiscoveryResult.get_device_count</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t132">edge_device_fleet_manager\discovery\core.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t132"><data value='init__'>DiscoveryProtocol.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t137">edge_device_fleet_manager\discovery\core.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t137"><data value='discover'>DiscoveryProtocol.discover</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t142">edge_device_fleet_manager\discovery\core.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t142"><data value='is_available'>DiscoveryProtocol.is_available</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t146">edge_device_fleet_manager\discovery\core.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t146"><data value='get_name'>DiscoveryProtocol.get_name</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t154">edge_device_fleet_manager\discovery\core.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t154"><data value='init__'>DeviceRegistry.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t160">edge_device_fleet_manager\discovery\core.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t160"><data value='add_device'>DeviceRegistry.add_device</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t196">edge_device_fleet_manager\discovery\core.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t196"><data value='get_device'>DeviceRegistry.get_device</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t201">edge_device_fleet_manager\discovery\core.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t201"><data value='get_device_by_ip'>DeviceRegistry.get_device_by_ip</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t209">edge_device_fleet_manager\discovery\core.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t209"><data value='get_all_devices'>DeviceRegistry.get_all_devices</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t214">edge_device_fleet_manager\discovery\core.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t214"><data value='remove_device'>DeviceRegistry.remove_device</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t226">edge_device_fleet_manager\discovery\core.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t226"><data value='cleanup_stale_devices'>DeviceRegistry.cleanup_stale_devices</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t245">edge_device_fleet_manager\discovery\core.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t245"><data value='get_device_count'>DeviceRegistry.get_device_count</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t254">edge_device_fleet_manager\discovery\core.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t254"><data value='init__'>DiscoveryEngine.__init__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t262">edge_device_fleet_manager\discovery\core.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t262"><data value='register_protocol'>DiscoveryEngine.register_protocol</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t267">edge_device_fleet_manager\discovery\core.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t267"><data value='discover_all'>DiscoveryEngine.discover_all</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t322">edge_device_fleet_manager\discovery\core.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t322"><data value='get_devices'>DiscoveryEngine.get_devices</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t326">edge_device_fleet_manager\discovery\core.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t326"><data value='cleanup_stale_devices'>DiscoveryEngine.cleanup_stale_devices</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html">edge_device_fleet_manager\discovery\core.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>77</td>
                <td>0</td>
                <td>4</td>
                <td class="right" data-ratio="77 77">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_exceptions_py.html">edge_device_fleet_manager\discovery\exceptions.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_exceptions_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e___init___py.html">edge_device_fleet_manager\discovery\protocols\__init__.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_mdns_py.html#t26">edge_device_fleet_manager\discovery\protocols\mdns.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_mdns_py.html#t26"><data value='build_query'>MDNSQuery.build_query</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_mdns_py.html#t52">edge_device_fleet_manager\discovery\protocols\mdns.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_mdns_py.html#t52"><data value='encode_domain_name'>MDNSQuery._encode_domain_name</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_mdns_py.html#t65">edge_device_fleet_manager\discovery\protocols\mdns.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_mdns_py.html#t65"><data value='init__'>MDNSResponse.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_mdns_py.html#t71">edge_device_fleet_manager\discovery\protocols\mdns.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_mdns_py.html#t71"><data value='parse'>MDNSResponse.parse</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_mdns_py.html#t101">edge_device_fleet_manager\discovery\protocols\mdns.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_mdns_py.html#t101"><data value='skip_question'>MDNSResponse._skip_question</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_mdns_py.html#t108">edge_device_fleet_manager\discovery\protocols\mdns.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_mdns_py.html#t108"><data value='parse_resource_record'>MDNSResponse._parse_resource_record</data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_mdns_py.html#t172">edge_device_fleet_manager\discovery\protocols\mdns.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_mdns_py.html#t172"><data value='parse_domain_name'>MDNSResponse._parse_domain_name</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_mdns_py.html#t199">edge_device_fleet_manager\discovery\protocols\mdns.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_mdns_py.html#t199"><data value='parse_domain_name_from_data'>MDNSResponse._parse_domain_name_from_data</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_mdns_py.html#t220">edge_device_fleet_manager\discovery\protocols\mdns.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_mdns_py.html#t220"><data value='skip_domain_name'>MDNSResponse._skip_domain_name</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_mdns_py.html#t234">edge_device_fleet_manager\discovery\protocols\mdns.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_mdns_py.html#t234"><data value='parse_txt_record'>MDNSResponse._parse_txt_record</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_mdns_py.html#t257">edge_device_fleet_manager\discovery\protocols\mdns.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_mdns_py.html#t257"><data value='create_devices'>MDNSResponse._create_devices</data></a></td>
                <td>40</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="0 40">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_mdns_py.html#t325">edge_device_fleet_manager\discovery\protocols\mdns.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_mdns_py.html#t325"><data value='determine_device_type'>MDNSResponse._determine_device_type</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_mdns_py.html#t370">edge_device_fleet_manager\discovery\protocols\mdns.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_mdns_py.html#t370"><data value='init__'>MDNSDiscovery.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_mdns_py.html#t375">edge_device_fleet_manager\discovery\protocols\mdns.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_mdns_py.html#t375"><data value='discover'>MDNSDiscovery.discover</data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_mdns_py.html#t428">edge_device_fleet_manager\discovery\protocols\mdns.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_mdns_py.html#t428"><data value='collect_responses'>MDNSDiscovery._collect_responses</data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_mdns_py.html#t475">edge_device_fleet_manager\discovery\protocols\mdns.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_mdns_py.html#t475"><data value='is_available'>MDNSDiscovery.is_available</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_mdns_py.html">edge_device_fleet_manager\discovery\protocols\mdns.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_mdns_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>34</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="34 34">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_network_scan_py.html#t63">edge_device_fleet_manager\discovery\protocols\network_scan.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_network_scan_py.html#t63"><data value='init__'>PortScanner.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_network_scan_py.html#t67">edge_device_fleet_manager\discovery\protocols\network_scan.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_network_scan_py.html#t67"><data value='scan_port'>PortScanner.scan_port</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_network_scan_py.html#t99">edge_device_fleet_manager\discovery\protocols\network_scan.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_network_scan_py.html#t99"><data value='scan_host'>PortScanner.scan_host</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_network_scan_py.html#t104">edge_device_fleet_manager\discovery\protocols\network_scan.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_network_scan_py.html#t104"><data value='scan_with_semaphore'>PortScanner.scan_host.scan_with_semaphore</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_network_scan_py.html#t143">edge_device_fleet_manager\discovery\protocols\network_scan.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_network_scan_py.html#t143"><data value='identify_service'>ServiceIdentifier.identify_service</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_network_scan_py.html#t191">edge_device_fleet_manager\discovery\protocols\network_scan.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_network_scan_py.html#t191"><data value='grab_banner'>ServiceIdentifier._grab_banner</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_network_scan_py.html#t223">edge_device_fleet_manager\discovery\protocols\network_scan.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_network_scan_py.html#t223"><data value='ping_host'>NetworkDiscovery.ping_host</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_network_scan_py.html#t248">edge_device_fleet_manager\discovery\protocols\network_scan.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_network_scan_py.html#t248"><data value='get_local_networks'>NetworkDiscovery.get_local_networks</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_network_scan_py.html#t280">edge_device_fleet_manager\discovery\protocols\network_scan.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_network_scan_py.html#t280"><data value='init__'>NetworkScanDiscovery.__init__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_network_scan_py.html#t291">edge_device_fleet_manager\discovery\protocols\network_scan.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_network_scan_py.html#t291"><data value='discover'>NetworkScanDiscovery.discover</data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_network_scan_py.html#t356">edge_device_fleet_manager\discovery\protocols\network_scan.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_network_scan_py.html#t356"><data value='scan_hosts'>NetworkScanDiscovery._scan_hosts</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_network_scan_py.html#t360">edge_device_fleet_manager\discovery\protocols\network_scan.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_network_scan_py.html#t360"><data value='scan_host_with_semaphore'>NetworkScanDiscovery._scan_hosts.scan_host_with_semaphore</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_network_scan_py.html#t372">edge_device_fleet_manager\discovery\protocols\network_scan.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_network_scan_py.html#t372"><data value='scan_single_host'>NetworkScanDiscovery._scan_single_host</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_network_scan_py.html#t423">edge_device_fleet_manager\discovery\protocols\network_scan.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_network_scan_py.html#t423"><data value='determine_device_type'>NetworkScanDiscovery._determine_device_type</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_network_scan_py.html#t458">edge_device_fleet_manager\discovery\protocols\network_scan.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_network_scan_py.html#t458"><data value='is_available'>NetworkScanDiscovery.is_available</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_network_scan_py.html">edge_device_fleet_manager\discovery\protocols\network_scan.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_network_scan_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>37</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="37 37">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_ssdp_py.html#t29">edge_device_fleet_manager\discovery\protocols\ssdp.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_ssdp_py.html#t29"><data value='build_msearch'>SSDPMessage.build_msearch</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_ssdp_py.html#t41">edge_device_fleet_manager\discovery\protocols\ssdp.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_ssdp_py.html#t41"><data value='parse_response'>SSDPMessage.parse_response</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_ssdp_py.html#t65">edge_device_fleet_manager\discovery\protocols\ssdp.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_ssdp_py.html#t65"><data value='fetch_device_description'>UPnPDeviceParser.fetch_device_description</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_ssdp_py.html#t79">edge_device_fleet_manager\discovery\protocols\ssdp.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_ssdp_py.html#t79"><data value='parse_device_xml'>UPnPDeviceParser.parse_device_xml</data></a></td>
                <td>36</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="0 36">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_ssdp_py.html#t161">edge_device_fleet_manager\discovery\protocols\ssdp.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_ssdp_py.html#t161"><data value='init__'>SSDPDiscovery.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_ssdp_py.html#t166">edge_device_fleet_manager\discovery\protocols\ssdp.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_ssdp_py.html#t166"><data value='discover'>SSDPDiscovery.discover</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_ssdp_py.html#t216">edge_device_fleet_manager\discovery\protocols\ssdp.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_ssdp_py.html#t216"><data value='discover_target'>SSDPDiscovery._discover_target</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_ssdp_py.html#t263">edge_device_fleet_manager\discovery\protocols\ssdp.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_ssdp_py.html#t263"><data value='create_device_from_response'>SSDPDiscovery._create_device_from_response</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_ssdp_py.html#t333">edge_device_fleet_manager\discovery\protocols\ssdp.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_ssdp_py.html#t333"><data value='determine_device_type'>SSDPDiscovery._determine_device_type</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_ssdp_py.html#t357">edge_device_fleet_manager\discovery\protocols\ssdp.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_ssdp_py.html#t357"><data value='is_available'>SSDPDiscovery.is_available</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_ssdp_py.html">edge_device_fleet_manager\discovery\protocols\ssdp.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_ssdp_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>32</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="32 32">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_rate_limiter_py.html#t34">edge_device_fleet_manager\discovery\rate_limiter.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_rate_limiter_py.html#t34"><data value='init__'>TokenBucket.__init__</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_rate_limiter_py.html#t41">edge_device_fleet_manager\discovery\rate_limiter.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_rate_limiter_py.html#t41"><data value='consume'>TokenBucket.consume</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_rate_limiter_py.html#t55">edge_device_fleet_manager\discovery\rate_limiter.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_rate_limiter_py.html#t55"><data value='wait_for_tokens'>TokenBucket.wait_for_tokens</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_rate_limiter_py.html#t78">edge_device_fleet_manager\discovery\rate_limiter.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_rate_limiter_py.html#t78"><data value='init__'>AdaptiveRateLimiter.__init__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_rate_limiter_py.html#t86">edge_device_fleet_manager\discovery\rate_limiter.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_rate_limiter_py.html#t86"><data value='get_host_bucket'>AdaptiveRateLimiter._get_host_bucket</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_rate_limiter_py.html#t95">edge_device_fleet_manager\discovery\rate_limiter.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_rate_limiter_py.html#t95"><data value='acquire'>AdaptiveRateLimiter.acquire</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_rate_limiter_py.html#t113">edge_device_fleet_manager\discovery\rate_limiter.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_rate_limiter_py.html#t113"><data value='record_success'>AdaptiveRateLimiter.record_success</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_rate_limiter_py.html#t125">edge_device_fleet_manager\discovery\rate_limiter.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_rate_limiter_py.html#t125"><data value='record_failure'>AdaptiveRateLimiter.record_failure</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_rate_limiter_py.html#t147">edge_device_fleet_manager\discovery\rate_limiter.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_rate_limiter_py.html#t147"><data value='get_host_stats'>AdaptiveRateLimiter.get_host_stats</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_rate_limiter_py.html#t172">edge_device_fleet_manager\discovery\rate_limiter.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_rate_limiter_py.html#t172"><data value='get_global_stats'>AdaptiveRateLimiter.get_global_stats</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_rate_limiter_py.html#t204">edge_device_fleet_manager\discovery\rate_limiter.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_rate_limiter_py.html#t204"><data value='init__'>RateLimiter.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_rate_limiter_py.html#t209">edge_device_fleet_manager\discovery\rate_limiter.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_rate_limiter_py.html#t209"><data value='aenter__'>RateLimiter.__aenter__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_rate_limiter_py.html#t212">edge_device_fleet_manager\discovery\rate_limiter.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_rate_limiter_py.html#t212"><data value='aexit__'>RateLimiter.__aexit__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_rate_limiter_py.html#t215">edge_device_fleet_manager\discovery\rate_limiter.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_rate_limiter_py.html#t215"><data value='acquire'>RateLimiter.acquire</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_rate_limiter_py.html#t219">edge_device_fleet_manager\discovery\rate_limiter.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_rate_limiter_py.html#t219"><data value='record_success'>RateLimiter.record_success</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_rate_limiter_py.html#t223">edge_device_fleet_manager\discovery\rate_limiter.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_rate_limiter_py.html#t223"><data value='record_failure'>RateLimiter.record_failure</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_rate_limiter_py.html#t227">edge_device_fleet_manager\discovery\rate_limiter.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_rate_limiter_py.html#t227"><data value='get_stats'>RateLimiter.get_stats</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_rate_limiter_py.html">edge_device_fleet_manager\discovery\rate_limiter.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_rate_limiter_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>36</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="36 36">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_50d91cd27b284a0f___init___py.html">edge_device_fleet_manager\plugins\__init__.py</a></td>
                <td class="name left"><a href="z_50d91cd27b284a0f___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_50d91cd27b284a0f_sample_plugin_py.html#t25">edge_device_fleet_manager\plugins\sample_plugin.py</a></td>
                <td class="name left"><a href="z_50d91cd27b284a0f_sample_plugin_py.html#t25"><data value='initialize'>SamplePlugin.initialize</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_50d91cd27b284a0f_sample_plugin_py.html#t29">edge_device_fleet_manager\plugins\sample_plugin.py</a></td>
                <td class="name left"><a href="z_50d91cd27b284a0f_sample_plugin_py.html#t29"><data value='cleanup'>SamplePlugin.cleanup</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_50d91cd27b284a0f_sample_plugin_py.html#t35">edge_device_fleet_manager\plugins\sample_plugin.py</a></td>
                <td class="name left"><a href="z_50d91cd27b284a0f_sample_plugin_py.html#t35"><data value='hello'>SamplePlugin.hello</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_50d91cd27b284a0f_sample_plugin_py.html#t42">edge_device_fleet_manager\plugins\sample_plugin.py</a></td>
                <td class="name left"><a href="z_50d91cd27b284a0f_sample_plugin_py.html#t42"><data value='status'>SamplePlugin.status</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_50d91cd27b284a0f_sample_plugin_py.html">edge_device_fleet_manager\plugins\sample_plugin.py</a></td>
                <td class="name left"><a href="z_50d91cd27b284a0f_sample_plugin_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_17de617221a3d7e6___init___py.html">edge_device_fleet_manager\repository\__init__.py</a></td>
                <td class="name left"><a href="z_17de617221a3d7e6___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233___init___py.html">edge_device_fleet_manager\repository\application\__init__.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t21">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t21"><data value='init__'>Command.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t30">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t30"><data value='command_type'>Command.command_type</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t38">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t38"><data value='init__'>RegisterDeviceCommand.__init__</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t55">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t55"><data value='command_type'>RegisterDeviceCommand.command_type</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t62">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t62"><data value='init__'>UpdateDeviceCommand.__init__</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t72">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t72"><data value='command_type'>UpdateDeviceCommand.command_type</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t79">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t79"><data value='init__'>DeactivateDeviceCommand.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t86">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t86"><data value='command_type'>DeactivateDeviceCommand.command_type</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t93">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t93"><data value='init__'>ActivateDeviceCommand.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t98">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t98"><data value='command_type'>ActivateDeviceCommand.command_type</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t109">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t109"><data value='command_type'>SetMaintenanceModeCommand.command_type</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t116">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t116"><data value='init__'>UpdateLocationCommand.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t123">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t123"><data value='command_type'>UpdateLocationCommand.command_type</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t130">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t130"><data value='init__'>UpdateCapabilitiesCommand.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t137">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t137"><data value='command_type'>UpdateCapabilitiesCommand.command_type</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t144">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t144"><data value='init__'>UpdateConfigurationCommand.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t152">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t152"><data value='command_type'>UpdateConfigurationCommand.command_type</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t164">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t164"><data value='command_type'>RemoveConfigurationCommand.command_type</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t171">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t171"><data value='init__'>RecordMetricsCommand.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t178">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t178"><data value='command_type'>RecordMetricsCommand.command_type</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t191">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t191"><data value='command_type'>DeleteDeviceCommand.command_type</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t203">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t203"><data value='command_type'>BulkUpdateDevicesCommand.command_type</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t216">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t216"><data value='command_type'>ImportDevicesCommand.command_type</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t230">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t230"><data value='command_type'>SyncDeviceCommand.command_type</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t239">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t239"><data value='validate_register_device_command'>CommandValidator.validate_register_device_command</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t259">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t259"><data value='validate_update_device_command'>CommandValidator.validate_update_device_command</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t272">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t272"><data value='validate_deactivate_device_command'>CommandValidator.validate_deactivate_device_command</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t285">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t285"><data value='validate_update_configuration_command'>CommandValidator.validate_update_configuration_command</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t317">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t317"><data value='success_result'>CommandResult.success_result</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t326">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t326"><data value='failure_result'>CommandResult.failure_result</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t341">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t341"><data value='validation_failure_result'>CommandResult.validation_failure_result</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>102</td>
                <td>0</td>
                <td>3</td>
                <td class="right" data-ratio="102 102">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_dto_py.html#t39">edge_device_fleet_manager\repository\application\dto.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_dto_py.html#t39"><data value='has_coordinates'>DeviceLocationDto.has_coordinates</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_dto_py.html#t44">edge_device_fleet_manager\repository\application\dto.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_dto_py.html#t44"><data value='has_physical_location'>DeviceLocationDto.has_physical_location</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_dto_py.html#t65">edge_device_fleet_manager\repository\application\dto.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_dto_py.html#t65"><data value='post_init__'>DeviceCapabilitiesDto.__post_init__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_dto_py.html#t90">edge_device_fleet_manager\repository\application\dto.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_dto_py.html#t90"><data value='post_init__'>DeviceMetricsDto.__post_init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_dto_py.html#t130">edge_device_fleet_manager\repository\application\dto.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_dto_py.html#t130"><data value='from_aggregate'>DeviceDto.from_aggregate</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_dto_py.html#t331">edge_device_fleet_manager\repository\application\dto.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_dto_py.html#t331"><data value='device_aggregate_to_dto'>DtoConverter.device_aggregate_to_dto</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_dto_py.html#t340">edge_device_fleet_manager\repository\application\dto.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_dto_py.html#t340"><data value='device_aggregates_to_list_dto'>DtoConverter.device_aggregates_to_list_dto</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_dto_py.html">edge_device_fleet_manager\repository\application\dto.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_dto_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>154</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="154 154">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t30">edge_device_fleet_manager\repository\application\handlers.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t30"><data value='handle'>CommandHandler.handle</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t39">edge_device_fleet_manager\repository\application\handlers.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t39"><data value='handle'>QueryHandler.handle</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t47">edge_device_fleet_manager\repository\application\handlers.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t47"><data value='init__'>DeviceCommandHandler.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t53">edge_device_fleet_manager\repository\application\handlers.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t53"><data value='handle'>DeviceCommandHandler.handle</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t85">edge_device_fleet_manager\repository\application\handlers.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t85"><data value='handle_register_device'>DeviceCommandHandler._handle_register_device</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t125">edge_device_fleet_manager\repository\application\handlers.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t125"><data value='handle_update_device'>DeviceCommandHandler._handle_update_device</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t153">edge_device_fleet_manager\repository\application\handlers.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t153"><data value='handle_deactivate_device'>DeviceCommandHandler._handle_deactivate_device</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t185">edge_device_fleet_manager\repository\application\handlers.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t185"><data value='handle_activate_device'>DeviceCommandHandler._handle_activate_device</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t212">edge_device_fleet_manager\repository\application\handlers.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t212"><data value='handle_update_location'>DeviceCommandHandler._handle_update_location</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t234">edge_device_fleet_manager\repository\application\handlers.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t234"><data value='handle_update_capabilities'>DeviceCommandHandler._handle_update_capabilities</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t265">edge_device_fleet_manager\repository\application\handlers.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t265"><data value='handle_update_configuration'>DeviceCommandHandler._handle_update_configuration</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t296">edge_device_fleet_manager\repository\application\handlers.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t296"><data value='handle_record_metrics'>DeviceCommandHandler._handle_record_metrics</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t322">edge_device_fleet_manager\repository\application\handlers.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t322"><data value='init__'>DeviceQueryHandler.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t326">edge_device_fleet_manager\repository\application\handlers.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t326"><data value='handle'>DeviceQueryHandler.handle</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t351">edge_device_fleet_manager\repository\application\handlers.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t351"><data value='handle_get_device'>DeviceQueryHandler._handle_get_device</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t373">edge_device_fleet_manager\repository\application\handlers.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t373"><data value='handle_list_devices'>DeviceQueryHandler._handle_list_devices</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t411">edge_device_fleet_manager\repository\application\handlers.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t411"><data value='handle_search_devices'>DeviceQueryHandler._handle_search_devices</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t463">edge_device_fleet_manager\repository\application\handlers.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t463"><data value='sort_devices'>DeviceQueryHandler._sort_devices</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t482">edge_device_fleet_manager\repository\application\handlers.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t482"><data value='apply_search_filters'>DeviceQueryHandler._apply_search_filters</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t507">edge_device_fleet_manager\repository\application\handlers.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t507"><data value='get_applied_filters'>DeviceQueryHandler._get_applied_filters</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html">edge_device_fleet_manager\repository\application\handlers.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>42</td>
                <td>0</td>
                <td>4</td>
                <td class="right" data-ratio="42 42">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t21">edge_device_fleet_manager\repository\application\queries.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t21"><data value='init__'>Query.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t30">edge_device_fleet_manager\repository\application\queries.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t30"><data value='query_type'>Query.query_type</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t38">edge_device_fleet_manager\repository\application\queries.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t38"><data value='init__'>GetDeviceQuery.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t46">edge_device_fleet_manager\repository\application\queries.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t46"><data value='query_type'>GetDeviceQuery.query_type</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t59">edge_device_fleet_manager\repository\application\queries.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t59"><data value='query_type'>GetDeviceBySerialNumberQuery.query_type</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t66">edge_device_fleet_manager\repository\application\queries.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t66"><data value='init__'>ListDevicesQuery.__init__</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t78">edge_device_fleet_manager\repository\application\queries.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t78"><data value='query_type'>ListDevicesQuery.query_type</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t85">edge_device_fleet_manager\repository\application\queries.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t85"><data value='init__'>SearchDevicesQuery.__init__</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t117">edge_device_fleet_manager\repository\application\queries.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t117"><data value='query_type'>SearchDevicesQuery.query_type</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t131">edge_device_fleet_manager\repository\application\queries.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t131"><data value='query_type'>GetDevicesByTypeQuery.query_type</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t145">edge_device_fleet_manager\repository\application\queries.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t145"><data value='query_type'>GetDevicesByStatusQuery.query_type</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t159">edge_device_fleet_manager\repository\application\queries.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t159"><data value='query_type'>GetStaleDevicesQuery.query_type</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t174">edge_device_fleet_manager\repository\application\queries.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t174"><data value='query_type'>GetDeviceMetricsQuery.query_type</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t186">edge_device_fleet_manager\repository\application\queries.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t186"><data value='query_type'>GetDeviceConfigurationQuery.query_type</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t203">edge_device_fleet_manager\repository\application\queries.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t203"><data value='query_type'>GetDevicesWithCapabilitiesQuery.query_type</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t222">edge_device_fleet_manager\repository\application\queries.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t222"><data value='query_type'>GetDevicesByLocationQuery.query_type</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t234">edge_device_fleet_manager\repository\application\queries.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t234"><data value='query_type'>GetDeviceStatisticsQuery.query_type</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t249">edge_device_fleet_manager\repository\application\queries.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t249"><data value='query_type'>GetDeviceHealthQuery.query_type</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t258">edge_device_fleet_manager\repository\application\queries.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t258"><data value='validate_pagination'>QueryValidator.validate_pagination</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t273">edge_device_fleet_manager\repository\application\queries.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t273"><data value='validate_sort_parameters'>QueryValidator.validate_sort_parameters</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t291">edge_device_fleet_manager\repository\application\queries.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t291"><data value='validate_date_range'>QueryValidator.validate_date_range</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t304">edge_device_fleet_manager\repository\application\queries.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t304"><data value='validate_location_query'>QueryValidator.validate_location_query</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t341">edge_device_fleet_manager\repository\application\queries.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t341"><data value='failure_result'>QueryResult.failure_result</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t368">edge_device_fleet_manager\repository\application\queries.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t368"><data value='create'>PaginatedQueryResult.create</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html">edge_device_fleet_manager\repository\application\queries.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>133</td>
                <td>0</td>
                <td>3</td>
                <td class="right" data-ratio="133 133">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_services_py.html#t31">edge_device_fleet_manager\repository\application\services.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_services_py.html#t31"><data value='init__'>DeviceApplicationService.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_services_py.html#t36">edge_device_fleet_manager\repository\application\services.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_services_py.html#t36"><data value='register_device'>DeviceApplicationService.register_device</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_services_py.html#t73">edge_device_fleet_manager\repository\application\services.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_services_py.html#t73"><data value='update_device'>DeviceApplicationService.update_device</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_services_py.html#t94">edge_device_fleet_manager\repository\application\services.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_services_py.html#t94"><data value='deactivate_device'>DeviceApplicationService.deactivate_device</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_services_py.html#t111">edge_device_fleet_manager\repository\application\services.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_services_py.html#t111"><data value='activate_device'>DeviceApplicationService.activate_device</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_services_py.html#t126">edge_device_fleet_manager\repository\application\services.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_services_py.html#t126"><data value='update_device_location'>DeviceApplicationService.update_device_location</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_services_py.html#t143">edge_device_fleet_manager\repository\application\services.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_services_py.html#t143"><data value='update_device_capabilities'>DeviceApplicationService.update_device_capabilities</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_services_py.html#t160">edge_device_fleet_manager\repository\application\services.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_services_py.html#t160"><data value='update_device_configuration'>DeviceApplicationService.update_device_configuration</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_services_py.html#t179">edge_device_fleet_manager\repository\application\services.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_services_py.html#t179"><data value='record_device_metrics'>DeviceApplicationService.record_device_metrics</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_services_py.html#t197">edge_device_fleet_manager\repository\application\services.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_services_py.html#t197"><data value='get_device'>DeviceApplicationService.get_device</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_services_py.html#t216">edge_device_fleet_manager\repository\application\services.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_services_py.html#t216"><data value='list_devices'>DeviceApplicationService.list_devices</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_services_py.html#t241">edge_device_fleet_manager\repository\application\services.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_services_py.html#t241"><data value='search_devices'>DeviceApplicationService.search_devices</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_services_py.html#t272">edge_device_fleet_manager\repository\application\services.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_services_py.html#t272"><data value='get_devices_by_type'>DeviceApplicationService.get_devices_by_type</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_services_py.html#t293">edge_device_fleet_manager\repository\application\services.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_services_py.html#t293"><data value='get_devices_by_status'>DeviceApplicationService.get_devices_by_status</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_services_py.html#t318">edge_device_fleet_manager\repository\application\services.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_services_py.html#t318"><data value='init__'>DeviceQueryService.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_services_py.html#t321">edge_device_fleet_manager\repository\application\services.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_services_py.html#t321"><data value='get_device_summary'>DeviceQueryService.get_device_summary</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_services_py.html#t343">edge_device_fleet_manager\repository\application\services.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_services_py.html#t343"><data value='get_device_statistics'>DeviceQueryService.get_device_statistics</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_services_py.html#t382">edge_device_fleet_manager\repository\application\services.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_services_py.html#t382"><data value='get_location_summary'>DeviceQueryService._get_location_summary</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_services_py.html#t404">edge_device_fleet_manager\repository\application\services.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_services_py.html#t404"><data value='get_capabilities_summary'>DeviceQueryService._get_capabilities_summary</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_services_py.html">edge_device_fleet_manager\repository\application\services.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_services_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>31</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="31 31">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79___init___py.html">edge_device_fleet_manager\repository\domain\__init__.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t56">edge_device_fleet_manager\repository\domain\entities.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t56"><data value='update_configuration'>DeviceConfiguration.update_configuration</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t72">edge_device_fleet_manager\repository\domain\entities.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t72"><data value='remove_configuration'>DeviceConfiguration.remove_configuration</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t89">edge_device_fleet_manager\repository\domain\entities.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t89"><data value='get_configuration'>DeviceConfiguration.get_configuration</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t113">edge_device_fleet_manager\repository\domain\entities.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t113"><data value='post_init__'>DeviceEntity.__post_init__</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t123">edge_device_fleet_manager\repository\domain\entities.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t123"><data value='update_name'>DeviceEntity.update_name</data></a></td>
                <td>8</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="6 8">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t142">edge_device_fleet_manager\repository\domain\entities.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t142"><data value='update_location'>DeviceEntity.update_location</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t155">edge_device_fleet_manager\repository\domain\entities.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t155"><data value='update_capabilities'>DeviceEntity.update_capabilities</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t168">edge_device_fleet_manager\repository\domain\entities.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t168"><data value='deactivate'>DeviceEntity.deactivate</data></a></td>
                <td>5</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="4 5">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t183">edge_device_fleet_manager\repository\domain\entities.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t183"><data value='activate'>DeviceEntity.activate</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t197">edge_device_fleet_manager\repository\domain\entities.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t197"><data value='set_maintenance_mode'>DeviceEntity.set_maintenance_mode</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t213">edge_device_fleet_manager\repository\domain\entities.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t213"><data value='record_metrics'>DeviceEntity.record_metrics</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t224">edge_device_fleet_manager\repository\domain\entities.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t224"><data value='update_last_seen'>DeviceEntity.update_last_seen</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t229">edge_device_fleet_manager\repository\domain\entities.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t229"><data value='is_online'>DeviceEntity.is_online</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t237">edge_device_fleet_manager\repository\domain\entities.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t237"><data value='update_version'>DeviceEntity._update_version</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t257">edge_device_fleet_manager\repository\domain\entities.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t257"><data value='post_init__'>DeviceGroup.__post_init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t264">edge_device_fleet_manager\repository\domain\entities.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t264"><data value='add_device'>DeviceGroup.add_device</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t270">edge_device_fleet_manager\repository\domain\entities.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t270"><data value='remove_device'>DeviceGroup.remove_device</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t278">edge_device_fleet_manager\repository\domain\entities.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t278"><data value='update_name'>DeviceGroup.update_name</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t289">edge_device_fleet_manager\repository\domain\entities.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t289"><data value='update_description'>DeviceGroup.update_description</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t294">edge_device_fleet_manager\repository\domain\entities.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t294"><data value='get_device_count'>DeviceGroup.get_device_count</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t298">edge_device_fleet_manager\repository\domain\entities.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t298"><data value='update_version'>DeviceGroup._update_version</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t307">edge_device_fleet_manager\repository\domain\entities.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t307"><data value='init__'>DeviceAggregate.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t313">edge_device_fleet_manager\repository\domain\entities.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t313"><data value='device_id'>DeviceAggregate.device_id</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t318">edge_device_fleet_manager\repository\domain\entities.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t318"><data value='device'>DeviceAggregate.device</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t323">edge_device_fleet_manager\repository\domain\entities.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t323"><data value='version'>DeviceAggregate.version</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t328">edge_device_fleet_manager\repository\domain\entities.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t328"><data value='create'>DeviceAggregate.create</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t369">edge_device_fleet_manager\repository\domain\entities.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t369"><data value='update_name'>DeviceAggregate.update_name</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t374">edge_device_fleet_manager\repository\domain\entities.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t374"><data value='update_location'>DeviceAggregate.update_location</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t379">edge_device_fleet_manager\repository\domain\entities.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t379"><data value='update_capabilities'>DeviceAggregate.update_capabilities</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t384">edge_device_fleet_manager\repository\domain\entities.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t384"><data value='deactivate'>DeviceAggregate.deactivate</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t389">edge_device_fleet_manager\repository\domain\entities.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t389"><data value='activate'>DeviceAggregate.activate</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t394">edge_device_fleet_manager\repository\domain\entities.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t394"><data value='set_maintenance_mode'>DeviceAggregate.set_maintenance_mode</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t399">edge_device_fleet_manager\repository\domain\entities.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t399"><data value='record_metrics'>DeviceAggregate.record_metrics</data></a></td>
                <td>5</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="4 5">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t410">edge_device_fleet_manager\repository\domain\entities.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t410"><data value='update_configuration'>DeviceAggregate.update_configuration</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t416">edge_device_fleet_manager\repository\domain\entities.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t416"><data value='get_configuration'>DeviceAggregate.get_configuration</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t422">edge_device_fleet_manager\repository\domain\entities.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t422"><data value='get_recent_metrics'>DeviceAggregate.get_recent_metrics</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t426">edge_device_fleet_manager\repository\domain\entities.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t426"><data value='get_uncommitted_events'>DeviceAggregate.get_uncommitted_events</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t430">edge_device_fleet_manager\repository\domain\entities.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t430"><data value='mark_events_as_committed'>DeviceAggregate.mark_events_as_committed</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t434">edge_device_fleet_manager\repository\domain\entities.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t434"><data value='add_event'>DeviceAggregate._add_event</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html">edge_device_fleet_manager\repository\domain\entities.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>100</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="100 100">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t20">edge_device_fleet_manager\repository\domain\events.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t20"><data value='init__'>DomainEvent.__init__</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t29">edge_device_fleet_manager\repository\domain\events.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t29"><data value='event_type'>DomainEvent.event_type</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t33">edge_device_fleet_manager\repository\domain\events.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t33"><data value='to_dict'>DomainEvent.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t45">edge_device_fleet_manager\repository\domain\events.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t45"><data value='get_event_data'>DomainEvent._get_event_data</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t53">edge_device_fleet_manager\repository\domain\events.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t53"><data value='init__'>DeviceRegisteredEvent.__init__</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t67">edge_device_fleet_manager\repository\domain\events.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t67"><data value='event_type'>DeviceRegisteredEvent.event_type</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t70">edge_device_fleet_manager\repository\domain\events.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t70"><data value='get_event_data'>DeviceRegisteredEvent._get_event_data</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t115">edge_device_fleet_manager\repository\domain\events.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t115"><data value='init__'>DeviceUpdatedEvent.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t122">edge_device_fleet_manager\repository\domain\events.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t122"><data value='event_type'>DeviceUpdatedEvent.event_type</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t125">edge_device_fleet_manager\repository\domain\events.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t125"><data value='get_event_data'>DeviceUpdatedEvent._get_event_data</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t135">edge_device_fleet_manager\repository\domain\events.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t135"><data value='init__'>DeviceDeactivatedEvent.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t142">edge_device_fleet_manager\repository\domain\events.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t142"><data value='event_type'>DeviceDeactivatedEvent.event_type</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t145">edge_device_fleet_manager\repository\domain\events.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t145"><data value='get_event_data'>DeviceDeactivatedEvent._get_event_data</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t155">edge_device_fleet_manager\repository\domain\events.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t155"><data value='init__'>DeviceActivatedEvent.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t160">edge_device_fleet_manager\repository\domain\events.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t160"><data value='event_type'>DeviceActivatedEvent.event_type</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t163">edge_device_fleet_manager\repository\domain\events.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t163"><data value='get_event_data'>DeviceActivatedEvent._get_event_data</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t172">edge_device_fleet_manager\repository\domain\events.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t172"><data value='init__'>DeviceConfigurationChangedEvent.__init__</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t181">edge_device_fleet_manager\repository\domain\events.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t181"><data value='event_type'>DeviceConfigurationChangedEvent.event_type</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t184">edge_device_fleet_manager\repository\domain\events.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t184"><data value='get_event_data'>DeviceConfigurationChangedEvent._get_event_data</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t196">edge_device_fleet_manager\repository\domain\events.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t196"><data value='init__'>DeviceMetricsRecordedEvent.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t201">edge_device_fleet_manager\repository\domain\events.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t201"><data value='event_type'>DeviceMetricsRecordedEvent.event_type</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t204">edge_device_fleet_manager\repository\domain\events.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t204"><data value='get_event_data'>DeviceMetricsRecordedEvent._get_event_data</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t218">edge_device_fleet_manager\repository\domain\events.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t218"><data value='event_type'>DeviceLocationChangedEvent.event_type</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t221">edge_device_fleet_manager\repository\domain\events.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t221"><data value='get_event_data'>DeviceLocationChangedEvent._get_event_data</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t256">edge_device_fleet_manager\repository\domain\events.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t256"><data value='event_type'>DeviceCapabilitiesUpdatedEvent.event_type</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t259">edge_device_fleet_manager\repository\domain\events.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t259"><data value='get_event_data'>DeviceCapabilitiesUpdatedEvent._get_event_data</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html">edge_device_fleet_manager\repository\domain\events.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>53</td>
                <td>0</td>
                <td>5</td>
                <td class="right" data-ratio="53 53">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_services_py.html#t21">edge_device_fleet_manager\repository\domain\services.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_services_py.html#t21"><data value='validate_device_name'>DeviceValidationService.validate_device_name</data></a></td>
                <td>12</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="11 12">92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_services_py.html#t44">edge_device_fleet_manager\repository\domain\services.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_services_py.html#t44"><data value='validate_serial_number'>DeviceValidationService.validate_serial_number</data></a></td>
                <td>9</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="8 9">89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_services_py.html#t62">edge_device_fleet_manager\repository\domain\services.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_services_py.html#t62"><data value='validate_device_type_compatibility'>DeviceValidationService.validate_device_type_compatibility</data></a></td>
                <td>14</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="3 14">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_services_py.html#t86">edge_device_fleet_manager\repository\domain\services.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_services_py.html#t86"><data value='init__'>DeviceRegistrationService.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_services_py.html#t89">edge_device_fleet_manager\repository\domain\services.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_services_py.html#t89"><data value='validate_registration_data'>DeviceRegistrationService.validate_registration_data</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_services_py.html#t107">edge_device_fleet_manager\repository\domain\services.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_services_py.html#t107"><data value='create_device_aggregate'>DeviceRegistrationService.create_device_aggregate</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_services_py.html#t136">edge_device_fleet_manager\repository\domain\services.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_services_py.html#t136"><data value='check_duplicate_identifier'>DeviceRegistrationService.check_duplicate_identifier</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_services_py.html#t157">edge_device_fleet_manager\repository\domain\services.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_services_py.html#t157"><data value='init__'>DeviceLifecycleService.__init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_services_py.html#t160">edge_device_fleet_manager\repository\domain\services.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_services_py.html#t160"><data value='can_deactivate_device'>DeviceLifecycleService.can_deactivate_device</data></a></td>
                <td>5</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="4 5">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_services_py.html#t170">edge_device_fleet_manager\repository\domain\services.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_services_py.html#t170"><data value='can_activate_device'>DeviceLifecycleService.can_activate_device</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_services_py.html#t180">edge_device_fleet_manager\repository\domain\services.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_services_py.html#t180"><data value='can_set_maintenance_mode'>DeviceLifecycleService.can_set_maintenance_mode</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_services_py.html#t190">edge_device_fleet_manager\repository\domain\services.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_services_py.html#t190"><data value='can_decommission_device'>DeviceLifecycleService.can_decommission_device</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_services_py.html#t197">edge_device_fleet_manager\repository\domain\services.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_services_py.html#t197"><data value='get_stale_devices'>DeviceLifecycleService.get_stale_devices</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_services_py.html#t217">edge_device_fleet_manager\repository\domain\services.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_services_py.html#t217"><data value='get_devices_by_status'>DeviceLifecycleService.get_devices_by_status</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_services_py.html#t225">edge_device_fleet_manager\repository\domain\services.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_services_py.html#t225"><data value='get_devices_by_type'>DeviceLifecycleService.get_devices_by_type</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_services_py.html#t233">edge_device_fleet_manager\repository\domain\services.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_services_py.html#t233"><data value='get_devices_by_manufacturer'>DeviceLifecycleService.get_devices_by_manufacturer</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_services_py.html#t245">edge_device_fleet_manager\repository\domain\services.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_services_py.html#t245"><data value='get_devices_with_capabilities'>DeviceLifecycleService.get_devices_with_capabilities</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_services_py.html#t281">edge_device_fleet_manager\repository\domain\services.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_services_py.html#t281"><data value='calculate_device_health_score'>DeviceLifecycleService.calculate_device_health_score</data></a></td>
                <td>18</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="13 18">72%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_services_py.html">edge_device_fleet_manager\repository\domain\services.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_services_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>30</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="30 30">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t24">edge_device_fleet_manager\repository\domain\value_objects.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t24"><data value='post_init__'>DeviceId.__post_init__</data></a></td>
                <td>2</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="1 2">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t29">edge_device_fleet_manager\repository\domain\value_objects.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t29"><data value='generate'>DeviceId.generate</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t34">edge_device_fleet_manager\repository\domain\value_objects.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t34"><data value='from_string'>DeviceId.from_string</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t41">edge_device_fleet_manager\repository\domain\value_objects.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t41"><data value='str__'>DeviceId.__str__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t53">edge_device_fleet_manager\repository\domain\value_objects.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t53"><data value='post_init__'>DeviceIdentifier.__post_init__</data></a></td>
                <td>6</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="5 6">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t64">edge_device_fleet_manager\repository\domain\value_objects.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t64"><data value='is_valid_mac_address'>DeviceIdentifier._is_valid_mac_address</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t69">edge_device_fleet_manager\repository\domain\value_objects.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t69"><data value='str__'>DeviceIdentifier.__str__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t90">edge_device_fleet_manager\repository\domain\value_objects.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t90"><data value='post_init__'>DeviceLocation.__post_init__</data></a></td>
                <td>8</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="7 8">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t103">edge_device_fleet_manager\repository\domain\value_objects.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t103"><data value='has_coordinates'>DeviceLocation.has_coordinates</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t108">edge_device_fleet_manager\repository\domain\value_objects.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t108"><data value='has_physical_location'>DeviceLocation.has_physical_location</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t112">edge_device_fleet_manager\repository\domain\value_objects.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t112"><data value='str__'>DeviceLocation.__str__</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t150">edge_device_fleet_manager\repository\domain\value_objects.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t150"><data value='post_init__'>DeviceCapabilities.__post_init__</data></a></td>
                <td>11</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="8 11">73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t170">edge_device_fleet_manager\repository\domain\value_objects.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t170"><data value='has_sensors'>DeviceCapabilities.has_sensors</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t175">edge_device_fleet_manager\repository\domain\value_objects.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t175"><data value='has_actuators'>DeviceCapabilities.has_actuators</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t180">edge_device_fleet_manager\repository\domain\value_objects.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t180"><data value='is_battery_powered'>DeviceCapabilities.is_battery_powered</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t184">edge_device_fleet_manager\repository\domain\value_objects.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t184"><data value='supports_protocol'>DeviceCapabilities.supports_protocol</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t188">edge_device_fleet_manager\repository\domain\value_objects.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t188"><data value='str__'>DeviceCapabilities.__str__</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t221">edge_device_fleet_manager\repository\domain\value_objects.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t221"><data value='post_init__'>DeviceMetrics.__post_init__</data></a></td>
                <td>12</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="10 12">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t251">edge_device_fleet_manager\repository\domain\value_objects.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t251"><data value='create_now'>DeviceMetrics.create_now</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t256">edge_device_fleet_manager\repository\domain\value_objects.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t256"><data value='age_seconds'>DeviceMetrics.age_seconds</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t261">edge_device_fleet_manager\repository\domain\value_objects.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t261"><data value='is_recent'>DeviceMetrics.is_recent</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t265">edge_device_fleet_manager\repository\domain\value_objects.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t265"><data value='to_dict'>DeviceMetrics.to_dict</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t284">edge_device_fleet_manager\repository\domain\value_objects.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t284"><data value='str__'>DeviceMetrics.__str__</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html">edge_device_fleet_manager\repository\domain\value_objects.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>83</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="83 83">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7___init___py.html">edge_device_fleet_manager\repository\infrastructure\__init__.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_database_py.html#t29">edge_device_fleet_manager\repository\infrastructure\database.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_database_py.html#t29"><data value='init__'>DatabaseSession.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_database_py.html#t34">edge_device_fleet_manager\repository\infrastructure\database.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_database_py.html#t34"><data value='get_session'>DatabaseSession.get_session</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_database_py.html#t46">edge_device_fleet_manager\repository\infrastructure\database.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_database_py.html#t46"><data value='create_session'>DatabaseSession.create_session</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_database_py.html#t50">edge_device_fleet_manager\repository\infrastructure\database.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_database_py.html#t50"><data value='create_tables'>DatabaseSession.create_tables</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_database_py.html#t54">edge_device_fleet_manager\repository\infrastructure\database.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_database_py.html#t54"><data value='drop_tables'>DatabaseSession.drop_tables</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_database_py.html#t59">edge_device_fleet_manager\repository\infrastructure\database.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_database_py.html#t59"><data value='create_database_engine'>create_database_engine</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_database_py.html#t123">edge_device_fleet_manager\repository\infrastructure\database.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_database_py.html#t123"><data value='get_database_url_from_config'>get_database_url_from_config</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_database_py.html#t140">edge_device_fleet_manager\repository\infrastructure\database.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_database_py.html#t140"><data value='init__'>DatabaseMigration.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_database_py.html#t143">edge_device_fleet_manager\repository\infrastructure\database.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_database_py.html#t143"><data value='create_schema'>DatabaseMigration.create_schema</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_database_py.html#t150">edge_device_fleet_manager\repository\infrastructure\database.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_database_py.html#t150"><data value='drop_schema'>DatabaseMigration.drop_schema</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_database_py.html#t157">edge_device_fleet_manager\repository\infrastructure\database.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_database_py.html#t157"><data value='check_schema_exists'>DatabaseMigration.check_schema_exists</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_database_py.html#t171">edge_device_fleet_manager\repository\infrastructure\database.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_database_py.html#t171"><data value='get_schema_version'>DatabaseMigration.get_schema_version</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_database_py.html#t178">edge_device_fleet_manager\repository\infrastructure\database.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_database_py.html#t178"><data value='create_test_database'>create_test_database</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_database_py.html#t193">edge_device_fleet_manager\repository\infrastructure\database.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_database_py.html#t193"><data value='create_development_database'>create_development_database</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_database_py.html#t206">edge_device_fleet_manager\repository\infrastructure\database.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_database_py.html#t206"><data value='create_production_database'>create_production_database</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_database_py.html#t225">edge_device_fleet_manager\repository\infrastructure\database.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_database_py.html#t225"><data value='check_database_health'>check_database_health</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_database_py.html#t259">edge_device_fleet_manager\repository\infrastructure\database.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_database_py.html#t259"><data value='get_database_info'>get_database_info</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_database_py.html">edge_device_fleet_manager\repository\infrastructure\database.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_database_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>30</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="30 30">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html#t46">edge_device_fleet_manager\repository\infrastructure\event_store.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html#t46"><data value='repr__'>StoredEvent.__repr__</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html#t54">edge_device_fleet_manager\repository\infrastructure\event_store.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html#t54"><data value='save_events'>EventStore.save_events</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html#t59">edge_device_fleet_manager\repository\infrastructure\event_store.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html#t59"><data value='get_events'>EventStore.get_events</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html#t64">edge_device_fleet_manager\repository\infrastructure\event_store.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html#t64"><data value='get_all_events'>EventStore.get_all_events</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html#t69">edge_device_fleet_manager\repository\infrastructure\event_store.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html#t69"><data value='get_events_by_type'>EventStore.get_events_by_type</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html#t77">edge_device_fleet_manager\repository\infrastructure\event_store.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html#t77"><data value='init__'>InMemoryEventStore.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html#t81">edge_device_fleet_manager\repository\infrastructure\event_store.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html#t81"><data value='save_events'>InMemoryEventStore.save_events</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html#t102">edge_device_fleet_manager\repository\infrastructure\event_store.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html#t102"><data value='get_events'>InMemoryEventStore.get_events</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html#t111">edge_device_fleet_manager\repository\infrastructure\event_store.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html#t111"><data value='get_all_events'>InMemoryEventStore.get_all_events</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html#t121">edge_device_fleet_manager\repository\infrastructure\event_store.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html#t121"><data value='get_events_by_type'>InMemoryEventStore.get_events_by_type</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html#t126">edge_device_fleet_manager\repository\infrastructure\event_store.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html#t126"><data value='clear'>InMemoryEventStore.clear</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html#t135">edge_device_fleet_manager\repository\infrastructure\event_store.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html#t135"><data value='init__'>SqlEventStore.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html#t139">edge_device_fleet_manager\repository\infrastructure\event_store.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html#t139"><data value='register_event_type'>SqlEventStore.register_event_type</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html#t152">edge_device_fleet_manager\repository\infrastructure\event_store.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html#t152"><data value='save_events'>SqlEventStore.save_events</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html#t190">edge_device_fleet_manager\repository\infrastructure\event_store.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html#t190"><data value='get_events'>SqlEventStore.get_events</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html#t207">edge_device_fleet_manager\repository\infrastructure\event_store.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html#t207"><data value='get_all_events'>SqlEventStore.get_all_events</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html#t226">edge_device_fleet_manager\repository\infrastructure\event_store.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html#t226"><data value='get_events_by_type'>SqlEventStore.get_events_by_type</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html#t245">edge_device_fleet_manager\repository\infrastructure\event_store.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html#t245"><data value='deserialize_event'>SqlEventStore._deserialize_event</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html">edge_device_fleet_manager\repository\infrastructure\event_store.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>41</td>
                <td>0</td>
                <td>9</td>
                <td class="right" data-ratio="41 41">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t67">edge_device_fleet_manager\repository\infrastructure\repositories.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t67"><data value='repr__'>DeviceModel.__repr__</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t86">edge_device_fleet_manager\repository\infrastructure\repositories.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t86"><data value='repr__'>DeviceGroupModel.__repr__</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t94">edge_device_fleet_manager\repository\infrastructure\repositories.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t94"><data value='save'>DeviceRepository.save</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t99">edge_device_fleet_manager\repository\infrastructure\repositories.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t99"><data value='get_by_id'>DeviceRepository.get_by_id</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t104">edge_device_fleet_manager\repository\infrastructure\repositories.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t104"><data value='get_by_serial_number'>DeviceRepository.get_by_serial_number</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t109">edge_device_fleet_manager\repository\infrastructure\repositories.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t109"><data value='get_all'>DeviceRepository.get_all</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t114">edge_device_fleet_manager\repository\infrastructure\repositories.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t114"><data value='find_by_criteria'>DeviceRepository.find_by_criteria</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t119">edge_device_fleet_manager\repository\infrastructure\repositories.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t119"><data value='delete'>DeviceRepository.delete</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t128">edge_device_fleet_manager\repository\infrastructure\repositories.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t128"><data value='save'>DeviceGroupRepository.save</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t133">edge_device_fleet_manager\repository\infrastructure\repositories.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t133"><data value='get_by_id'>DeviceGroupRepository.get_by_id</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t138">edge_device_fleet_manager\repository\infrastructure\repositories.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t138"><data value='get_all'>DeviceGroupRepository.get_all</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t143">edge_device_fleet_manager\repository\infrastructure\repositories.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t143"><data value='delete'>DeviceGroupRepository.delete</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t151">edge_device_fleet_manager\repository\infrastructure\repositories.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t151"><data value='init__'>InMemoryDeviceRepository.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t154">edge_device_fleet_manager\repository\infrastructure\repositories.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t154"><data value='save'>InMemoryDeviceRepository.save</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t158">edge_device_fleet_manager\repository\infrastructure\repositories.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t158"><data value='get_by_id'>InMemoryDeviceRepository.get_by_id</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t162">edge_device_fleet_manager\repository\infrastructure\repositories.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t162"><data value='get_by_serial_number'>InMemoryDeviceRepository.get_by_serial_number</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t169">edge_device_fleet_manager\repository\infrastructure\repositories.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t169"><data value='get_all'>InMemoryDeviceRepository.get_all</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t173">edge_device_fleet_manager\repository\infrastructure\repositories.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t173"><data value='find_by_criteria'>InMemoryDeviceRepository.find_by_criteria</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t201">edge_device_fleet_manager\repository\infrastructure\repositories.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t201"><data value='delete'>InMemoryDeviceRepository.delete</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t209">edge_device_fleet_manager\repository\infrastructure\repositories.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t209"><data value='clear'>InMemoryDeviceRepository.clear</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t217">edge_device_fleet_manager\repository\infrastructure\repositories.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t217"><data value='init__'>SqlDeviceRepository.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t221">edge_device_fleet_manager\repository\infrastructure\repositories.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t221"><data value='save'>SqlDeviceRepository.save</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t259">edge_device_fleet_manager\repository\infrastructure\repositories.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t259"><data value='get_by_id'>SqlDeviceRepository.get_by_id</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t278">edge_device_fleet_manager\repository\infrastructure\repositories.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t278"><data value='get_by_serial_number'>SqlDeviceRepository.get_by_serial_number</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t297">edge_device_fleet_manager\repository\infrastructure\repositories.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t297"><data value='get_all'>SqlDeviceRepository.get_all</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t310">edge_device_fleet_manager\repository\infrastructure\repositories.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t310"><data value='find_by_criteria'>SqlDeviceRepository.find_by_criteria</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t336">edge_device_fleet_manager\repository\infrastructure\repositories.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t336"><data value='delete'>SqlDeviceRepository.delete</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t354">edge_device_fleet_manager\repository\infrastructure\repositories.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t354"><data value='create_device_model'>SqlDeviceRepository._create_device_model</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t398">edge_device_fleet_manager\repository\infrastructure\repositories.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t398"><data value='update_device_model'>SqlDeviceRepository._update_device_model</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t441">edge_device_fleet_manager\repository\infrastructure\repositories.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t441"><data value='create_aggregate_from_model'>SqlDeviceRepository._create_aggregate_from_model</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html">edge_device_fleet_manager\repository\infrastructure\repositories.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>70</td>
                <td>0</td>
                <td>22</td>
                <td class="right" data-ratio="70 70">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t29">edge_device_fleet_manager\repository\infrastructure\unit_of_work.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t29"><data value='aenter__'>UnitOfWork.__aenter__</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t34">edge_device_fleet_manager\repository\infrastructure\unit_of_work.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t34"><data value='aexit__'>UnitOfWork.__aexit__</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t39">edge_device_fleet_manager\repository\infrastructure\unit_of_work.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t39"><data value='commit'>UnitOfWork.commit</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t44">edge_device_fleet_manager\repository\infrastructure\unit_of_work.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t44"><data value='rollback'>UnitOfWork.rollback</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t49">edge_device_fleet_manager\repository\infrastructure\unit_of_work.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t49"><data value='collect_new_events'>UnitOfWork.collect_new_events</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t57">edge_device_fleet_manager\repository\infrastructure\unit_of_work.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t57"><data value='init__'>SqlUnitOfWork.__init__</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t64">edge_device_fleet_manager\repository\infrastructure\unit_of_work.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t64"><data value='aenter__'>SqlUnitOfWork.__aenter__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t74">edge_device_fleet_manager\repository\infrastructure\unit_of_work.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t74"><data value='aexit__'>SqlUnitOfWork.__aexit__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t84">edge_device_fleet_manager\repository\infrastructure\unit_of_work.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t84"><data value='commit'>SqlUnitOfWork.commit</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t111">edge_device_fleet_manager\repository\infrastructure\unit_of_work.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t111"><data value='rollback'>SqlUnitOfWork.rollback</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t119">edge_device_fleet_manager\repository\infrastructure\unit_of_work.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t119"><data value='collect_new_events'>SqlUnitOfWork.collect_new_events</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t126">edge_device_fleet_manager\repository\infrastructure\unit_of_work.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t126"><data value='track_aggregate'>SqlUnitOfWork.track_aggregate</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t131">edge_device_fleet_manager\repository\infrastructure\unit_of_work.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t131"><data value='publish_events'>SqlUnitOfWork._publish_events</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t143">edge_device_fleet_manager\repository\infrastructure\unit_of_work.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t143"><data value='init__'>InMemoryUnitOfWork.__init__</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t153">edge_device_fleet_manager\repository\infrastructure\unit_of_work.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t153"><data value='aenter__'>InMemoryUnitOfWork.__aenter__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t157">edge_device_fleet_manager\repository\infrastructure\unit_of_work.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t157"><data value='aexit__'>InMemoryUnitOfWork.__aexit__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t164">edge_device_fleet_manager\repository\infrastructure\unit_of_work.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t164"><data value='commit'>InMemoryUnitOfWork.commit</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t193">edge_device_fleet_manager\repository\infrastructure\unit_of_work.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t193"><data value='rollback'>InMemoryUnitOfWork.rollback</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t198">edge_device_fleet_manager\repository\infrastructure\unit_of_work.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t198"><data value='collect_new_events'>InMemoryUnitOfWork.collect_new_events</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t205">edge_device_fleet_manager\repository\infrastructure\unit_of_work.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t205"><data value='track_aggregate'>InMemoryUnitOfWork.track_aggregate</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t210">edge_device_fleet_manager\repository\infrastructure\unit_of_work.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t210"><data value='publish_events'>InMemoryUnitOfWork._publish_events</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t216">edge_device_fleet_manager\repository\infrastructure\unit_of_work.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t216"><data value='clear'>InMemoryUnitOfWork.clear</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t226">edge_device_fleet_manager\repository\infrastructure\unit_of_work.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t226"><data value='create_unit_of_work'>create_unit_of_work</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t243">edge_device_fleet_manager\repository\infrastructure\unit_of_work.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t243"><data value='init__'>UnitOfWorkFactory.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t247">edge_device_fleet_manager\repository\infrastructure\unit_of_work.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t247"><data value='create'>UnitOfWorkFactory.create</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t252">edge_device_fleet_manager\repository\infrastructure\unit_of_work.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t252"><data value='get_unit_of_work'>UnitOfWorkFactory.get_unit_of_work</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t264">edge_device_fleet_manager\repository\infrastructure\unit_of_work.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t264"><data value='handle'>EventHandler.handle</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t272">edge_device_fleet_manager\repository\infrastructure\unit_of_work.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t272"><data value='init__'>EventDispatcher.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t275">edge_device_fleet_manager\repository\infrastructure\unit_of_work.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t275"><data value='register_handler'>EventDispatcher.register_handler</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t281">edge_device_fleet_manager\repository\infrastructure\unit_of_work.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t281"><data value='dispatch'>EventDispatcher.dispatch</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t293">edge_device_fleet_manager\repository\infrastructure\unit_of_work.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t293"><data value='dispatch_events'>EventDispatcher.dispatch_events</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t303">edge_device_fleet_manager\repository\infrastructure\unit_of_work.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t303"><data value='handle'>DeviceRegisteredEventHandler.handle</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t312">edge_device_fleet_manager\repository\infrastructure\unit_of_work.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t312"><data value='handle'>DeviceMetricsRecordedEventHandler.handle</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html">edge_device_fleet_manager\repository\infrastructure\unit_of_work.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>49</td>
                <td>0</td>
                <td>12</td>
                <td class="right" data-ratio="49 49">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4e142099aadd2379___init___py.html">edge_device_fleet_manager\utils\__init__.py</a></td>
                <td class="name left"><a href="z_4e142099aadd2379___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t18">edge_device_fleet_manager\utils\decorators.py</a></td>
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t18"><data value='with_correlation_id'>with_correlation_id</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t22">edge_device_fleet_manager\utils\decorators.py</a></td>
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t22"><data value='wrapper'>with_correlation_id.wrapper</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t28">edge_device_fleet_manager\utils\decorators.py</a></td>
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t28"><data value='async_wrapper'>with_correlation_id.async_wrapper</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t39">edge_device_fleet_manager\utils\decorators.py</a></td>
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t39"><data value='log_execution_time'>log_execution_time</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t42">edge_device_fleet_manager\utils\decorators.py</a></td>
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t42"><data value='decorator'>log_execution_time.decorator</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t46">edge_device_fleet_manager\utils\decorators.py</a></td>
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t46"><data value='wrapper'>log_execution_time.decorator.wrapper</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t71">edge_device_fleet_manager\utils\decorators.py</a></td>
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t71"><data value='async_wrapper'>log_execution_time.decorator.async_wrapper</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t103">edge_device_fleet_manager\utils\decorators.py</a></td>
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t103"><data value='retry'>retry</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t111">edge_device_fleet_manager\utils\decorators.py</a></td>
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t111"><data value='decorator'>retry.decorator</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t113">edge_device_fleet_manager\utils\decorators.py</a></td>
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t113"><data value='wrapper'>retry.decorator.wrapper</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t144">edge_device_fleet_manager\utils\decorators.py</a></td>
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t144"><data value='async_wrapper'>retry.decorator.async_wrapper</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t182">edge_device_fleet_manager\utils\decorators.py</a></td>
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t182"><data value='validate_config'>validate_config</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t186">edge_device_fleet_manager\utils\decorators.py</a></td>
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t186"><data value='wrapper'>validate_config.wrapper</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t195">edge_device_fleet_manager\utils\decorators.py</a></td>
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t195"><data value='async_wrapper'>validate_config.async_wrapper</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t209">edge_device_fleet_manager\utils\decorators.py</a></td>
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t209"><data value='handle_exceptions'>handle_exceptions</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t216">edge_device_fleet_manager\utils\decorators.py</a></td>
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t216"><data value='decorator'>handle_exceptions.decorator</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t218">edge_device_fleet_manager\utils\decorators.py</a></td>
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t218"><data value='wrapper'>handle_exceptions.decorator.wrapper</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t235">edge_device_fleet_manager\utils\decorators.py</a></td>
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html#t235"><data value='async_wrapper'>handle_exceptions.decorator.async_wrapper</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html">edge_device_fleet_manager\utils\decorators.py</a></td>
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>4811</td>
                <td>3038</td>
                <td>123</td>
                <td class="right" data-ratio="1773 4811">37%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-07-16 17:46 +0530
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>

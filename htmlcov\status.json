{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.8.2", "globals": "71445b50d2f8317cbd172427e505a0c3", "files": {"z_73bda9688a204f7a___init___py": {"hash": "bad6285428270999016747f80ceffc04", "index": {"url": "z_73bda9688a204f7a___init___py.html", "file": "edge_device_fleet_manager\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 7, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_df8cb9752f6d308c___init___py": {"hash": "e6abf8094086785292b22c20da71863b", "index": {"url": "z_df8cb9752f6d308c___init___py.html", "file": "edge_device_fleet_manager\\cli\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_df8cb9752f6d308c_main_py": {"hash": "432df2090473f7cc0e2bdf1a414997ab", "index": {"url": "z_df8cb9752f6d308c_main_py.html", "file": "edge_device_fleet_manager\\cli\\main.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 163, "n_excluded": 2, "n_missing": 125, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_df8cb9752f6d308c_types_py": {"hash": "306a7af7684e105fa778ac1cd3d2d67a", "index": {"url": "z_df8cb9752f6d308c_types_py.html", "file": "edge_device_fleet_manager\\cli\\types.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 136, "n_excluded": 0, "n_missing": 136, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_daf88cc4cd5d4988_config_py": {"hash": "67e83c512501ffde53e9500a577a0206", "index": {"url": "z_daf88cc4cd5d4988_config_py.html", "file": "edge_device_fleet_manager\\core\\config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 268, "n_excluded": 0, "n_missing": 156, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_daf88cc4cd5d4988_context_py": {"hash": "6cee12e936c4d0548f5e1a15c0495ea2", "index": {"url": "z_daf88cc4cd5d4988_context_py.html", "file": "edge_device_fleet_manager\\core\\context.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 169, "n_excluded": 0, "n_missing": 99, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_daf88cc4cd5d4988_exceptions_py": {"hash": "fe9d632ab5b6cd65d96dfc39c2d4a3e1", "index": {"url": "z_daf88cc4cd5d4988_exceptions_py.html", "file": "edge_device_fleet_manager\\core\\exceptions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 35, "n_excluded": 0, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_daf88cc4cd5d4988_logging_py": {"hash": "3fc9faea95a2b23adb3366e70206ca9b", "index": {"url": "z_daf88cc4cd5d4988_logging_py.html", "file": "edge_device_fleet_manager\\core\\logging.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 125, "n_excluded": 0, "n_missing": 82, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_daf88cc4cd5d4988_plugins_py": {"hash": "ed6a23bc6ec8aced572443ae55282210", "index": {"url": "z_daf88cc4cd5d4988_plugins_py.html", "file": "edge_device_fleet_manager\\core\\plugins.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 215, "n_excluded": 0, "n_missing": 83, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_50d91cd27b284a0f___init___py": {"hash": "1b891e03014fcdb12d93bc9411105339", "index": {"url": "z_50d91cd27b284a0f___init___py.html", "file": "edge_device_fleet_manager\\plugins\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_50d91cd27b284a0f_sample_plugin_py": {"hash": "4b18f08251d2fc616cafa9889c42916c", "index": {"url": "z_50d91cd27b284a0f_sample_plugin_py.html", "file": "edge_device_fleet_manager\\plugins\\sample_plugin.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 25, "n_excluded": 0, "n_missing": 25, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4e142099aadd2379___init___py": {"hash": "66df790bac5efb1bfcb89febe6fce06b", "index": {"url": "z_4e142099aadd2379___init___py.html", "file": "edge_device_fleet_manager\\utils\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4e142099aadd2379_decorators_py": {"hash": "3af43bf5e062f64671e0169a76578f9f", "index": {"url": "z_4e142099aadd2379_decorators_py.html", "file": "edge_device_fleet_manager\\utils\\decorators.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 127, "n_excluded": 0, "n_missing": 127, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}
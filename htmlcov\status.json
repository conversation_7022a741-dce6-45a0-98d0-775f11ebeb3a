{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.8.2", "globals": "71445b50d2f8317cbd172427e505a0c3", "files": {"z_73bda9688a204f7a___init___py": {"hash": "bad6285428270999016747f80ceffc04", "index": {"url": "z_73bda9688a204f7a___init___py.html", "file": "edge_device_fleet_manager\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 7, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_df8cb9752f6d308c___init___py": {"hash": "e6abf8094086785292b22c20da71863b", "index": {"url": "z_df8cb9752f6d308c___init___py.html", "file": "edge_device_fleet_manager\\cli\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_df8cb9752f6d308c_main_py": {"hash": "2c0b13e5c37838292c12f4afd12c1f0f", "index": {"url": "z_df8cb9752f6d308c_main_py.html", "file": "edge_device_fleet_manager\\cli\\main.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 214, "n_excluded": 2, "n_missing": 156, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_df8cb9752f6d308c_types_py": {"hash": "306a7af7684e105fa778ac1cd3d2d67a", "index": {"url": "z_df8cb9752f6d308c_types_py.html", "file": "edge_device_fleet_manager\\cli\\types.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 136, "n_excluded": 0, "n_missing": 136, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_daf88cc4cd5d4988_config_py": {"hash": "8bc46df9a813c34cd8369ef49cd0c40e", "index": {"url": "z_daf88cc4cd5d4988_config_py.html", "file": "edge_device_fleet_manager\\core\\config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 271, "n_excluded": 0, "n_missing": 159, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_daf88cc4cd5d4988_context_py": {"hash": "d5ae7a755122cdf283883c68248ec613", "index": {"url": "z_daf88cc4cd5d4988_context_py.html", "file": "edge_device_fleet_manager\\core\\context.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 169, "n_excluded": 0, "n_missing": 100, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_daf88cc4cd5d4988_exceptions_py": {"hash": "6160333c4db904e5df17176e54d6696f", "index": {"url": "z_daf88cc4cd5d4988_exceptions_py.html", "file": "edge_device_fleet_manager\\core\\exceptions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 35, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_daf88cc4cd5d4988_logging_py": {"hash": "3fc9faea95a2b23adb3366e70206ca9b", "index": {"url": "z_daf88cc4cd5d4988_logging_py.html", "file": "edge_device_fleet_manager\\core\\logging.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 125, "n_excluded": 0, "n_missing": 82, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_daf88cc4cd5d4988_plugins_py": {"hash": "f7a1c9e6dde331935ce66222ff803e9b", "index": {"url": "z_daf88cc4cd5d4988_plugins_py.html", "file": "edge_device_fleet_manager\\core\\plugins.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 215, "n_excluded": 0, "n_missing": 164, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_50d91cd27b284a0f___init___py": {"hash": "1b891e03014fcdb12d93bc9411105339", "index": {"url": "z_50d91cd27b284a0f___init___py.html", "file": "edge_device_fleet_manager\\plugins\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_50d91cd27b284a0f_sample_plugin_py": {"hash": "4b18f08251d2fc616cafa9889c42916c", "index": {"url": "z_50d91cd27b284a0f_sample_plugin_py.html", "file": "edge_device_fleet_manager\\plugins\\sample_plugin.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 25, "n_excluded": 0, "n_missing": 25, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4e142099aadd2379___init___py": {"hash": "66df790bac5efb1bfcb89febe6fce06b", "index": {"url": "z_4e142099aadd2379___init___py.html", "file": "edge_device_fleet_manager\\utils\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4e142099aadd2379_decorators_py": {"hash": "3af43bf5e062f64671e0169a76578f9f", "index": {"url": "z_4e142099aadd2379_decorators_py.html", "file": "edge_device_fleet_manager\\utils\\decorators.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 127, "n_excluded": 0, "n_missing": 127, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_93be9be7fbc09c3d___init___py": {"hash": "2521064605d31e806c2becb8779b790c", "index": {"url": "z_93be9be7fbc09c3d___init___py.html", "file": "edge_device_fleet_manager\\discovery\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 6, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_93be9be7fbc09c3d_cache_py": {"hash": "0cdd6703a4e9cf03b92cd74e3818b836", "index": {"url": "z_93be9be7fbc09c3d_cache_py.html", "file": "edge_device_fleet_manager\\discovery\\cache.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 200, "n_excluded": 0, "n_missing": 159, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_93be9be7fbc09c3d_core_py": {"hash": "f109f78c8eb809fb880c13c75d8c36f7", "index": {"url": "z_93be9be7fbc09c3d_core_py.html", "file": "edge_device_fleet_manager\\discovery\\core.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 176, "n_excluded": 8, "n_missing": 99, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_93be9be7fbc09c3d_exceptions_py": {"hash": "e1afb35a2e7e5e1deda666d6f6bca303", "index": {"url": "z_93be9be7fbc09c3d_exceptions_py.html", "file": "edge_device_fleet_manager\\discovery\\exceptions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 17, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_db3d818ed52c5b9e___init___py": {"hash": "6560af637fb1f39fc41696519096abe7", "index": {"url": "z_db3d818ed52c5b9e___init___py.html", "file": "edge_device_fleet_manager\\discovery\\protocols\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_db3d818ed52c5b9e_mdns_py": {"hash": "a9e55543ae6afb448099f66e647199d9", "index": {"url": "z_db3d818ed52c5b9e_mdns_py.html", "file": "edge_device_fleet_manager\\discovery\\protocols\\mdns.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 283, "n_excluded": 0, "n_missing": 249, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_db3d818ed52c5b9e_network_scan_py": {"hash": "8a673920028dce72975b85aa9aa626b2", "index": {"url": "z_db3d818ed52c5b9e_network_scan_py.html", "file": "edge_device_fleet_manager\\discovery\\protocols\\network_scan.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 232, "n_excluded": 0, "n_missing": 195, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_db3d818ed52c5b9e_ssdp_py": {"hash": "67a056fe30faefc7c8103998cb15ebd6", "index": {"url": "z_db3d818ed52c5b9e_ssdp_py.html", "file": "edge_device_fleet_manager\\discovery\\protocols\\ssdp.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 208, "n_excluded": 0, "n_missing": 176, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_93be9be7fbc09c3d_rate_limiter_py": {"hash": "4137434722e1c643835a91912f983f29", "index": {"url": "z_93be9be7fbc09c3d_rate_limiter_py.html", "file": "edge_device_fleet_manager\\discovery\\rate_limiter.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 117, "n_excluded": 0, "n_missing": 81, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_17de617221a3d7e6___init___py": {"hash": "652d840902448de3da373decaf12a517", "index": {"url": "z_17de617221a3d7e6___init___py.html", "file": "edge_device_fleet_manager\\repository\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8715c4b7d8e78233___init___py": {"hash": "eca8688fbe9398c6d3c6b30626c573ad", "index": {"url": "z_8715c4b7d8e78233___init___py.html", "file": "edge_device_fleet_manager\\repository\\application\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 6, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8715c4b7d8e78233_commands_py": {"hash": "1be7a92bdb80b762bf2850264e2ce88f", "index": {"url": "z_8715c4b7d8e78233_commands_py.html", "file": "edge_device_fleet_manager\\repository\\application\\commands.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 190, "n_excluded": 5, "n_missing": 88, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8715c4b7d8e78233_dto_py": {"hash": "5e8e2c1ae3f3dfd21538b8ef5233995b", "index": {"url": "z_8715c4b7d8e78233_dto_py.html", "file": "edge_device_fleet_manager\\repository\\application\\dto.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 189, "n_excluded": 0, "n_missing": 35, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8715c4b7d8e78233_handlers_py": {"hash": "ccf1fde5af9cfa26ce3a09a5e80ca1e0", "index": {"url": "z_8715c4b7d8e78233_handlers_py.html", "file": "edge_device_fleet_manager\\repository\\application\\handlers.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 252, "n_excluded": 8, "n_missing": 210, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8715c4b7d8e78233_queries_py": {"hash": "1ac025addac67b0f9f699bbe1aa5f35f", "index": {"url": "z_8715c4b7d8e78233_queries_py.html", "file": "edge_device_fleet_manager\\repository\\application\\queries.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 218, "n_excluded": 5, "n_missing": 85, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8715c4b7d8e78233_services_py": {"hash": "a56bf499b06957354fc6cbbe57604b53", "index": {"url": "z_8715c4b7d8e78233_services_py.html", "file": "edge_device_fleet_manager\\repository\\application\\services.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 101, "n_excluded": 0, "n_missing": 70, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b8bec98587f6fb79___init___py": {"hash": "96a143e41c072e348c7020f50aac0b01", "index": {"url": "z_b8bec98587f6fb79___init___py.html", "file": "edge_device_fleet_manager\\repository\\domain\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b8bec98587f6fb79_entities_py": {"hash": "e9a1f97c702f7fb2d57addb1fd1a1cf9", "index": {"url": "z_b8bec98587f6fb79_entities_py.html", "file": "edge_device_fleet_manager\\repository\\domain\\entities.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 222, "n_excluded": 0, "n_missing": 66, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b8bec98587f6fb79_events_py": {"hash": "f8be43677f1998525f0a35a191b6e15a", "index": {"url": "z_b8bec98587f6fb79_events_py.html", "file": "edge_device_fleet_manager\\repository\\domain\\events.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 108, "n_excluded": 9, "n_missing": 26, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b8bec98587f6fb79_services_py": {"hash": "c4a66b5fc6c071af392f7f3484649ea0", "index": {"url": "z_b8bec98587f6fb79_services_py.html", "file": "edge_device_fleet_manager\\repository\\domain\\services.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 147, "n_excluded": 0, "n_missing": 77, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b8bec98587f6fb79_value_objects_py": {"hash": "5ec08d52a0377d409c74cd12bd2dfca1", "index": {"url": "z_b8bec98587f6fb79_value_objects_py.html", "file": "edge_device_fleet_manager\\repository\\domain\\value_objects.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 184, "n_excluded": 0, "n_missing": 50, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8a0001c30d6026d7___init___py": {"hash": "5b1772de10e85833fa95523ae71696de", "index": {"url": "z_8a0001c30d6026d7___init___py.html", "file": "edge_device_fleet_manager\\repository\\infrastructure\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8a0001c30d6026d7_database_py": {"hash": "77214a687c809d70d91f722745e8bb1a", "index": {"url": "z_8a0001c30d6026d7_database_py.html", "file": "edge_device_fleet_manager\\repository\\infrastructure\\database.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 123, "n_excluded": 0, "n_missing": 93, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8a0001c30d6026d7_event_store_py": {"hash": "6943985fa8c18aea7054a99d4fb220f7", "index": {"url": "z_8a0001c30d6026d7_event_store_py.html", "file": "edge_device_fleet_manager\\repository\\infrastructure\\event_store.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 123, "n_excluded": 18, "n_missing": 82, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8a0001c30d6026d7_repositories_py": {"hash": "5f7957c5256ae482e6fdccdff8bdf5af", "index": {"url": "z_8a0001c30d6026d7_repositories_py.html", "file": "edge_device_fleet_manager\\repository\\infrastructure\\repositories.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 215, "n_excluded": 44, "n_missing": 145, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8a0001c30d6026d7_unit_of_work_py": {"hash": "51a4269ef39a5ed83cc0b2de624eb51b", "index": {"url": "z_8a0001c30d6026d7_unit_of_work_py.html", "file": "edge_device_fleet_manager\\repository\\infrastructure\\unit_of_work.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 152, "n_excluded": 24, "n_missing": 103, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}
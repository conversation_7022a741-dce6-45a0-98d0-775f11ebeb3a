[tox]
envlist = py38,py39,py310,py311,lint,type-check,security,docs
isolated_build = true
skip_missing_interpreters = true

[testenv]
deps = 
    pytest>=7.4.0
    pytest-asyncio>=0.21.0
    pytest-cov>=4.1.0
    pytest-mock>=3.11.0
    pytest-xdist>=3.3.0
    hypothesis>=6.82.0
    factory-boy>=3.3.0
commands = 
    pytest {posargs}

[testenv:py38]
basepython = python3.8

[testenv:py39]
basepython = python3.9

[testenv:py310]
basepython = python3.10

[testenv:py311]
basepython = python3.11

[testenv:lint]
deps = 
    flake8>=6.0.0
    flake8-docstrings
    flake8-bugbear
    flake8-comprehensions
    flake8-simplify
    flake8-bandit
    pylint>=2.17.0
commands = 
    flake8 edge_device_fleet_manager tests
    pylint edge_device_fleet_manager

[testenv:type-check]
deps = 
    mypy>=1.5.0
    types-PyYAML
    types-redis
    types-requests
    types-setuptools
commands = 
    mypy edge_device_fleet_manager

[testenv:security]
deps = 
    bandit>=1.7.0
    safety>=2.3.0
commands = 
    bandit -r edge_device_fleet_manager
    safety check

[testenv:format]
deps = 
    black>=23.7.0
    isort>=5.12.0
commands = 
    black edge_device_fleet_manager tests
    isort edge_device_fleet_manager tests

[testenv:format-check]
deps = 
    black>=23.7.0
    isort>=5.12.0
commands = 
    black --check edge_device_fleet_manager tests
    isort --check-only edge_device_fleet_manager tests

[testenv:docs]
deps = 
    mkdocs>=1.5.0
    mkdocs-material>=9.2.0
    mkdocs-mermaid2-plugin>=1.1.0
commands = 
    mkdocs build --strict

[testenv:coverage]
deps = 
    {[testenv]deps}
    coverage[toml]>=7.0.0
commands = 
    coverage run -m pytest
    coverage report
    coverage html
    coverage xml

[testenv:integration]
deps = 
    {[testenv]deps}
    docker>=6.1.0
commands = 
    pytest tests/integration {posargs}

[testenv:performance]
deps = 
    {[testenv]deps}
    memory-profiler>=0.61.0
    line-profiler>=4.1.0
commands = 
    pytest tests/performance {posargs}

# Parallel testing environments
[testenv:py38-parallel]
basepython = python3.8
deps = 
    {[testenv]deps}
commands = 
    pytest -n auto {posargs}

[testenv:py39-parallel]
basepython = python3.9
deps = 
    {[testenv]deps}
commands = 
    pytest -n auto {posargs}

[testenv:py310-parallel]
basepython = python3.10
deps = 
    {[testenv]deps}
commands = 
    pytest -n auto {posargs}

[testenv:py311-parallel]
basepython = python3.11
deps = 
    {[testenv]deps}
commands = 
    pytest -n auto {posargs}

# Environment-specific testing
[testenv:async]
deps = 
    {[testenv]deps}
commands = 
    pytest tests/unit/test_*async* tests/unit/test_plugins.py {posargs}

[testenv:db]
deps = 
    {[testenv]deps}
    sqlalchemy>=2.0.0
    alembic>=1.12.0
    asyncpg>=0.28.0
    psycopg2-binary>=2.9.0
commands = 
    pytest tests/unit/test_*db* tests/unit/test_*repository* {posargs}

[testenv:analytics]
deps = 
    {[testenv]deps}
    pandas>=2.0.0
    numpy>=1.24.0
    rxpy>=4.0.0
commands = 
    pytest tests/unit/test_*analytics* tests/unit/test_*telemetry* {posargs}

[testenv:report]
deps = 
    {[testenv]deps}
    jinja2>=3.1.0
    weasyprint>=60.0
    pypdf2>=3.0.0
commands = 
    pytest tests/unit/test_*report* tests/unit/test_*export* {posargs}

# Development environment
[testenv:dev]
deps = 
    {[testenv]deps}
    ipython>=8.14.0
    ipdb>=0.13.0
    pytest-watch>=4.2.0
usedevelop = true
commands = 
    {posargs:pytest}

# CI environment that runs everything
[testenv:ci]
deps = 
    {[testenv:lint]deps}
    {[testenv:type-check]deps}
    {[testenv:security]deps}
    {[testenv:coverage]deps}
commands = 
    flake8 edge_device_fleet_manager tests
    mypy edge_device_fleet_manager
    bandit -r edge_device_fleet_manager
    coverage run -m pytest
    coverage report --fail-under=90

[flake8]
max-line-length = 88
extend-ignore = E203, W503, E501
exclude = 
    .git,
    __pycache__,
    .venv,
    .tox,
    build,
    dist,
    *.egg-info

[coverage:run]
source = edge_device_fleet_manager
omit = 
    */tests/*
    */test_*
    */_version.py
    */migrations/*

[coverage:report]
exclude_lines = 
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod

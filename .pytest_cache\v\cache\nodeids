["tests/unit/test_config.py::TestConfig::test_config_from_env_vars", "tests/unit/test_config.py::TestConfig::test_config_validation", "tests/unit/test_config.py::TestConfig::test_default_config_creation", "tests/unit/test_config.py::TestConfigLoader::test_config_loader_initialization", "tests/unit/test_config.py::TestConfigLoader::test_config_loading_error_handling", "tests/unit/test_config.py::TestConfigLoader::test_load_config_with_environment_override", "tests/unit/test_config.py::TestConfigLoader::test_load_config_with_secrets", "tests/unit/test_config.py::TestConfigLoader::test_load_yaml_config", "tests/unit/test_config.py::TestGlobalConfigFunctions::test_get_config_reload", "tests/unit/test_config.py::TestGlobalConfigFunctions::test_get_config_singleton", "tests/unit/test_config.py::TestGlobalConfigFunctions::test_get_config_sync", "tests/unit/test_config.py::TestSecretsManager::test_check_rotation_needed", "tests/unit/test_config.py::TestSecretsManager::test_get_encryption_key_existing", "tests/unit/test_config.py::TestSecretsManager::test_get_encryption_key_new", "tests/unit/test_config.py::TestSecretsManager::test_get_secret", "tests/unit/test_config.py::TestSecretsManager::test_secrets_manager_initialization", "tests/unit/test_config.py::TestSecretsManager::test_set_secret", "tests/unit/test_discovery_cache.py::TestDiscoveryCache::test_cache_backend_selection", "tests/unit/test_discovery_cache.py::TestDiscoveryCache::test_cache_device", "tests/unit/test_discovery_cache.py::TestDiscoveryCache::test_cache_discovery_result", "tests/unit/test_discovery_cache.py::TestDiscoveryCache::test_clear_all", "tests/unit/test_discovery_cache.py::TestDiscoveryCache::test_dict_to_device_conversion", "tests/unit/test_discovery_cache.py::TestDiscoveryCache::test_get_cached_devices", "tests/unit/test_discovery_cache.py::TestDiscoveryCache::test_get_device_by_ip", "tests/unit/test_discovery_cache.py::TestDiscoveryCache::test_get_device_nonexistent", "tests/unit/test_discovery_cache.py::TestDiscoveryCache::test_get_discovery_result_nonexistent", "tests/unit/test_discovery_cache.py::TestDiscoveryCache::test_key_generation", "tests/unit/test_discovery_cache.py::TestDiscoveryCache::test_remove_device", "tests/unit/test_discovery_cache.py::TestMemoryCache::test_clear", "tests/unit/test_discovery_cache.py::TestMemoryCache::test_delete", "tests/unit/test_discovery_cache.py::TestMemoryCache::test_exists", "tests/unit/test_discovery_cache.py::TestMemoryCache::test_get_nonexistent", "tests/unit/test_discovery_cache.py::TestMemoryCache::test_keys_pattern", "tests/unit/test_discovery_cache.py::TestMemoryCache::test_set_and_get", "tests/unit/test_discovery_cache.py::TestMemoryCache::test_ttl_expiration", "tests/unit/test_discovery_cache.py::TestRedisCache::test_clear", "tests/unit/test_discovery_cache.py::TestRedisCache::test_delete", "tests/unit/test_discovery_cache.py::TestRedisCache::test_exists", "tests/unit/test_discovery_cache.py::TestRedisCache::test_get_nonexistent", "tests/unit/test_discovery_cache.py::TestRedisCache::test_keys", "tests/unit/test_discovery_cache.py::TestRedisCache::test_redis_error_handling", "tests/unit/test_discovery_cache.py::TestRedisCache::test_set_and_get", "tests/unit/test_discovery_core.py::TestDevice::test_device_creation", "tests/unit/test_discovery_core.py::TestDevice::test_device_is_stale", "tests/unit/test_discovery_core.py::TestDevice::test_device_to_dict", "tests/unit/test_discovery_core.py::TestDevice::test_device_update_last_seen", "tests/unit/test_discovery_core.py::TestDeviceRegistry::test_add_device", "tests/unit/test_discovery_core.py::TestDeviceRegistry::test_cleanup_stale_devices", "tests/unit/test_discovery_core.py::TestDeviceRegistry::test_device_merging", "tests/unit/test_discovery_core.py::TestDeviceRegistry::test_get_all_devices", "tests/unit/test_discovery_core.py::TestDeviceRegistry::test_get_device", "tests/unit/test_discovery_core.py::TestDeviceRegistry::test_get_device_by_ip", "tests/unit/test_discovery_core.py::TestDeviceRegistry::test_remove_device", "tests/unit/test_discovery_core.py::TestDiscoveryEngine::test_cleanup_stale_devices", "tests/unit/test_discovery_core.py::TestDiscoveryEngine::test_discover_all_success", "tests/unit/test_discovery_core.py::TestDiscoveryEngine::test_discover_all_with_failure", "tests/unit/test_discovery_core.py::TestDiscoveryEngine::test_discover_specific_protocols", "tests/unit/test_discovery_core.py::TestDiscoveryEngine::test_register_protocol", "tests/unit/test_discovery_core.py::TestDiscoveryResult::test_add_device", "tests/unit/test_discovery_core.py::TestDiscoveryResult::test_discovery_result_creation", "tests/unit/test_discovery_core.py::TestDiscoveryResult::test_get_device_count", "tests/unit/test_discovery_rate_limiter.py::TestAdaptiveRateLimiter::test_acquire_global_limit", "tests/unit/test_discovery_rate_limiter.py::TestAdaptiveRateLimiter::test_acquire_per_host_limit", "tests/unit/test_discovery_rate_limiter.py::TestAdaptiveRateLimiter::test_acquire_success", "tests/unit/test_discovery_rate_limiter.py::TestAdaptiveRateLimiter::test_backoff_decrease", "tests/unit/test_discovery_rate_limiter.py::TestAdaptiveRateLimiter::test_backoff_increase", "tests/unit/test_discovery_rate_limiter.py::TestAdaptiveRateLimiter::test_get_global_stats", "tests/unit/test_discovery_rate_limiter.py::TestAdaptiveRateLimiter::test_get_host_stats_empty", "tests/unit/test_discovery_rate_limiter.py::TestAdaptiveRateLimiter::test_record_failure", "tests/unit/test_discovery_rate_limiter.py::TestAdaptiveRateLimiter::test_record_success", "tests/unit/test_discovery_rate_limiter.py::TestRateLimitConfig::test_custom_config", "tests/unit/test_discovery_rate_limiter.py::TestRateLimitConfig::test_default_config", "tests/unit/test_discovery_rate_limiter.py::TestRateLimiter::test_acquire", "tests/unit/test_discovery_rate_limiter.py::TestRateLimiter::test_context_manager", "tests/unit/test_discovery_rate_limiter.py::TestRateLimiter::test_default_config", "tests/unit/test_discovery_rate_limiter.py::TestRateLimiter::test_get_stats_global", "tests/unit/test_discovery_rate_limiter.py::TestRateLimiter::test_get_stats_host", "tests/unit/test_discovery_rate_limiter.py::TestRateLimiter::test_rate_limiting_integration", "tests/unit/test_discovery_rate_limiter.py::TestRateLimiter::test_record_failure", "tests/unit/test_discovery_rate_limiter.py::TestRateLimiter::test_record_success", "tests/unit/test_discovery_rate_limiter.py::TestTokenBucket::test_consume_tokens", "tests/unit/test_discovery_rate_limiter.py::TestTokenBucket::test_token_bucket_creation", "tests/unit/test_discovery_rate_limiter.py::TestTokenBucket::test_token_refill", "tests/unit/test_discovery_rate_limiter.py::TestTokenBucket::test_wait_for_tokens", "tests/unit/test_discovery_rate_limiter.py::TestTokenBucket::test_wait_for_tokens_timeout", "tests/unit/test_plugins.py::TestPluginSystem::test_cli_integration_with_plugins", "tests/unit/test_plugins.py::TestPluginSystem::test_file_watcher_start_stop", "tests/unit/test_plugins.py::TestPluginSystem::test_plugin_base_class", "tests/unit/test_plugins.py::TestPluginSystem::test_plugin_commands_registration", "tests/unit/test_plugins.py::TestPluginSystem::test_plugin_load_error_continues_loading_others", "tests/unit/test_plugins.py::TestPluginSystem::test_plugin_loader_initialization", "tests/unit/test_plugins.py::TestPluginSystem::test_plugin_metadata", "tests/unit/test_plugins.py::TestPluginSystem::test_plugin_reload", "tests/unit/test_plugins.py::TestPluginSystem::test_plugin_timeout_handling", "tests/unit/test_plugins.py::TestPluginSystem::test_plugin_unloading", "tests/unit/test_plugins.py::TestPluginSystem::test_successful_plugin_loading"]
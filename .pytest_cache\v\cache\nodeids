["tests/unit/test_config.py::TestConfig::test_config_from_env_vars", "tests/unit/test_config.py::TestConfig::test_config_validation", "tests/unit/test_config.py::TestConfig::test_default_config_creation", "tests/unit/test_config.py::TestConfigLoader::test_config_loader_initialization", "tests/unit/test_config.py::TestConfigLoader::test_config_loading_error_handling", "tests/unit/test_config.py::TestConfigLoader::test_load_config_with_environment_override", "tests/unit/test_config.py::TestConfigLoader::test_load_config_with_secrets", "tests/unit/test_config.py::TestConfigLoader::test_load_yaml_config", "tests/unit/test_config.py::TestGlobalConfigFunctions::test_get_config_reload", "tests/unit/test_config.py::TestGlobalConfigFunctions::test_get_config_singleton", "tests/unit/test_config.py::TestGlobalConfigFunctions::test_get_config_sync", "tests/unit/test_config.py::TestSecretsManager::test_check_rotation_needed", "tests/unit/test_config.py::TestSecretsManager::test_get_encryption_key_existing", "tests/unit/test_config.py::TestSecretsManager::test_get_encryption_key_new", "tests/unit/test_config.py::TestSecretsManager::test_get_secret", "tests/unit/test_config.py::TestSecretsManager::test_secrets_manager_initialization", "tests/unit/test_config.py::TestSecretsManager::test_set_secret", "tests/unit/test_plugins.py::TestPluginSystem::test_cli_integration_with_plugins", "tests/unit/test_plugins.py::TestPluginSystem::test_file_watcher_start_stop", "tests/unit/test_plugins.py::TestPluginSystem::test_plugin_base_class", "tests/unit/test_plugins.py::TestPluginSystem::test_plugin_commands_registration", "tests/unit/test_plugins.py::TestPluginSystem::test_plugin_load_error_continues_loading_others", "tests/unit/test_plugins.py::TestPluginSystem::test_plugin_loader_initialization", "tests/unit/test_plugins.py::TestPluginSystem::test_plugin_metadata", "tests/unit/test_plugins.py::TestPluginSystem::test_plugin_reload", "tests/unit/test_plugins.py::TestPluginSystem::test_plugin_timeout_handling", "tests/unit/test_plugins.py::TestPluginSystem::test_plugin_unloading", "tests/unit/test_plugins.py::TestPluginSystem::test_successful_plugin_loading"]
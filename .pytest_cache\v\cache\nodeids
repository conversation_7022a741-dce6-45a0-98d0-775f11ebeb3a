["tests/unit/test_plugins.py::TestPluginSystem::test_cli_integration_with_plugins", "tests/unit/test_plugins.py::TestPluginSystem::test_file_watcher_start_stop", "tests/unit/test_plugins.py::TestPluginSystem::test_plugin_base_class", "tests/unit/test_plugins.py::TestPluginSystem::test_plugin_commands_registration", "tests/unit/test_plugins.py::TestPluginSystem::test_plugin_load_error_continues_loading_others", "tests/unit/test_plugins.py::TestPluginSystem::test_plugin_loader_initialization", "tests/unit/test_plugins.py::TestPluginSystem::test_plugin_metadata", "tests/unit/test_plugins.py::TestPluginSystem::test_plugin_reload", "tests/unit/test_plugins.py::TestPluginSystem::test_plugin_timeout_handling", "tests/unit/test_plugins.py::TestPluginSystem::test_plugin_unloading", "tests/unit/test_plugins.py::TestPluginSystem::test_successful_plugin_loading"]
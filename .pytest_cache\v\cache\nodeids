["tests/unit/test_config.py::TestConfig::test_config_from_env_vars", "tests/unit/test_config.py::TestConfig::test_config_validation", "tests/unit/test_config.py::TestConfig::test_default_config_creation", "tests/unit/test_config.py::TestConfigLoader::test_config_loader_initialization", "tests/unit/test_config.py::TestConfigLoader::test_config_loading_error_handling", "tests/unit/test_config.py::TestConfigLoader::test_load_config_with_environment_override", "tests/unit/test_config.py::TestConfigLoader::test_load_config_with_secrets", "tests/unit/test_config.py::TestConfigLoader::test_load_yaml_config", "tests/unit/test_config.py::TestGlobalConfigFunctions::test_get_config_reload", "tests/unit/test_config.py::TestGlobalConfigFunctions::test_get_config_singleton", "tests/unit/test_config.py::TestGlobalConfigFunctions::test_get_config_sync", "tests/unit/test_config.py::TestSecretsManager::test_check_rotation_needed", "tests/unit/test_config.py::TestSecretsManager::test_get_encryption_key_existing", "tests/unit/test_config.py::TestSecretsManager::test_get_encryption_key_new", "tests/unit/test_config.py::TestSecretsManager::test_get_secret", "tests/unit/test_config.py::TestSecretsManager::test_secrets_manager_initialization", "tests/unit/test_config.py::TestSecretsManager::test_set_secret", "tests/unit/test_discovery_cache.py::TestDiscoveryCache::test_cache_backend_selection", "tests/unit/test_discovery_cache.py::TestDiscoveryCache::test_cache_device", "tests/unit/test_discovery_cache.py::TestDiscoveryCache::test_cache_discovery_result", "tests/unit/test_discovery_cache.py::TestDiscoveryCache::test_clear_all", "tests/unit/test_discovery_cache.py::TestDiscoveryCache::test_dict_to_device_conversion", "tests/unit/test_discovery_cache.py::TestDiscoveryCache::test_get_cached_devices", "tests/unit/test_discovery_cache.py::TestDiscoveryCache::test_get_device_by_ip", "tests/unit/test_discovery_cache.py::TestDiscoveryCache::test_get_device_nonexistent", "tests/unit/test_discovery_cache.py::TestDiscoveryCache::test_get_discovery_result_nonexistent", "tests/unit/test_discovery_cache.py::TestDiscoveryCache::test_key_generation", "tests/unit/test_discovery_cache.py::TestDiscoveryCache::test_remove_device", "tests/unit/test_discovery_cache.py::TestMemoryCache::test_clear", "tests/unit/test_discovery_cache.py::TestMemoryCache::test_delete", "tests/unit/test_discovery_cache.py::TestMemoryCache::test_exists", "tests/unit/test_discovery_cache.py::TestMemoryCache::test_get_nonexistent", "tests/unit/test_discovery_cache.py::TestMemoryCache::test_keys_pattern", "tests/unit/test_discovery_cache.py::TestMemoryCache::test_set_and_get", "tests/unit/test_discovery_cache.py::TestMemoryCache::test_ttl_expiration", "tests/unit/test_discovery_cache.py::TestRedisCache::test_clear", "tests/unit/test_discovery_cache.py::TestRedisCache::test_delete", "tests/unit/test_discovery_cache.py::TestRedisCache::test_exists", "tests/unit/test_discovery_cache.py::TestRedisCache::test_get_nonexistent", "tests/unit/test_discovery_cache.py::TestRedisCache::test_keys", "tests/unit/test_discovery_cache.py::TestRedisCache::test_redis_error_handling", "tests/unit/test_discovery_cache.py::TestRedisCache::test_set_and_get", "tests/unit/test_discovery_core.py::TestDevice::test_device_creation", "tests/unit/test_discovery_core.py::TestDevice::test_device_is_stale", "tests/unit/test_discovery_core.py::TestDevice::test_device_to_dict", "tests/unit/test_discovery_core.py::TestDevice::test_device_update_last_seen", "tests/unit/test_discovery_core.py::TestDeviceRegistry::test_add_device", "tests/unit/test_discovery_core.py::TestDeviceRegistry::test_cleanup_stale_devices", "tests/unit/test_discovery_core.py::TestDeviceRegistry::test_device_merging", "tests/unit/test_discovery_core.py::TestDeviceRegistry::test_get_all_devices", "tests/unit/test_discovery_core.py::TestDeviceRegistry::test_get_device", "tests/unit/test_discovery_core.py::TestDeviceRegistry::test_get_device_by_ip", "tests/unit/test_discovery_core.py::TestDeviceRegistry::test_remove_device", "tests/unit/test_discovery_core.py::TestDiscoveryEngine::test_cleanup_stale_devices", "tests/unit/test_discovery_core.py::TestDiscoveryEngine::test_discover_all_success", "tests/unit/test_discovery_core.py::TestDiscoveryEngine::test_discover_all_with_failure", "tests/unit/test_discovery_core.py::TestDiscoveryEngine::test_discover_specific_protocols", "tests/unit/test_discovery_core.py::TestDiscoveryEngine::test_register_protocol", "tests/unit/test_discovery_core.py::TestDiscoveryResult::test_add_device", "tests/unit/test_discovery_core.py::TestDiscoveryResult::test_discovery_result_creation", "tests/unit/test_discovery_core.py::TestDiscoveryResult::test_get_device_count", "tests/unit/test_discovery_rate_limiter.py::TestAdaptiveRateLimiter::test_acquire_global_limit", "tests/unit/test_discovery_rate_limiter.py::TestAdaptiveRateLimiter::test_acquire_per_host_limit", "tests/unit/test_discovery_rate_limiter.py::TestAdaptiveRateLimiter::test_acquire_success", "tests/unit/test_discovery_rate_limiter.py::TestAdaptiveRateLimiter::test_backoff_decrease", "tests/unit/test_discovery_rate_limiter.py::TestAdaptiveRateLimiter::test_backoff_increase", "tests/unit/test_discovery_rate_limiter.py::TestAdaptiveRateLimiter::test_get_global_stats", "tests/unit/test_discovery_rate_limiter.py::TestAdaptiveRateLimiter::test_get_host_stats_empty", "tests/unit/test_discovery_rate_limiter.py::TestAdaptiveRateLimiter::test_record_failure", "tests/unit/test_discovery_rate_limiter.py::TestAdaptiveRateLimiter::test_record_success", "tests/unit/test_discovery_rate_limiter.py::TestRateLimitConfig::test_custom_config", "tests/unit/test_discovery_rate_limiter.py::TestRateLimitConfig::test_default_config", "tests/unit/test_discovery_rate_limiter.py::TestRateLimiter::test_acquire", "tests/unit/test_discovery_rate_limiter.py::TestRateLimiter::test_context_manager", "tests/unit/test_discovery_rate_limiter.py::TestRateLimiter::test_default_config", "tests/unit/test_discovery_rate_limiter.py::TestRateLimiter::test_get_stats_global", "tests/unit/test_discovery_rate_limiter.py::TestRateLimiter::test_get_stats_host", "tests/unit/test_discovery_rate_limiter.py::TestRateLimiter::test_rate_limiting_integration", "tests/unit/test_discovery_rate_limiter.py::TestRateLimiter::test_record_failure", "tests/unit/test_discovery_rate_limiter.py::TestRateLimiter::test_record_success", "tests/unit/test_discovery_rate_limiter.py::TestTokenBucket::test_consume_tokens", "tests/unit/test_discovery_rate_limiter.py::TestTokenBucket::test_token_bucket_creation", "tests/unit/test_discovery_rate_limiter.py::TestTokenBucket::test_token_refill", "tests/unit/test_discovery_rate_limiter.py::TestTokenBucket::test_wait_for_tokens", "tests/unit/test_discovery_rate_limiter.py::TestTokenBucket::test_wait_for_tokens_timeout", "tests/unit/test_plugins.py::TestPluginSystem::test_cli_integration_with_plugins", "tests/unit/test_plugins.py::TestPluginSystem::test_file_watcher_start_stop", "tests/unit/test_plugins.py::TestPluginSystem::test_plugin_base_class", "tests/unit/test_plugins.py::TestPluginSystem::test_plugin_commands_registration", "tests/unit/test_plugins.py::TestPluginSystem::test_plugin_load_error_continues_loading_others", "tests/unit/test_plugins.py::TestPluginSystem::test_plugin_loader_initialization", "tests/unit/test_plugins.py::TestPluginSystem::test_plugin_metadata", "tests/unit/test_plugins.py::TestPluginSystem::test_plugin_reload", "tests/unit/test_plugins.py::TestPluginSystem::test_plugin_timeout_handling", "tests/unit/test_plugins.py::TestPluginSystem::test_plugin_unloading", "tests/unit/test_plugins.py::TestPluginSystem::test_successful_plugin_loading", "tests/unit/test_repository_application.py::TestApplicationService::test_get_device", "tests/unit/test_repository_application.py::TestApplicationService::test_register_device", "tests/unit/test_repository_application.py::TestCommandHandler::test_handle_invalid_register_command", "tests/unit/test_repository_application.py::TestCommandHandler::test_handle_register_device_command", "tests/unit/test_repository_application.py::TestCommandHandler::test_handle_update_device_command", "tests/unit/test_repository_application.py::TestCommandResults::test_failure_result", "tests/unit/test_repository_application.py::TestCommandResults::test_success_result", "tests/unit/test_repository_application.py::TestCommandResults::test_validation_failure_result", "tests/unit/test_repository_application.py::TestCommandValidation::test_validate_register_device_command", "tests/unit/test_repository_application.py::TestCommandValidation::test_validate_update_device_command", "tests/unit/test_repository_application.py::TestCommands::test_deactivate_device_command", "tests/unit/test_repository_application.py::TestCommands::test_register_device_command", "tests/unit/test_repository_application.py::TestCommands::test_update_device_command", "tests/unit/test_repository_application.py::TestDTOs::test_device_dto_from_aggregate", "tests/unit/test_repository_application.py::TestDTOs::test_device_identifier_dto", "tests/unit/test_repository_application.py::TestDTOs::test_dto_converter", "tests/unit/test_repository_application.py::TestQueries::test_get_device_query", "tests/unit/test_repository_application.py::TestQueries::test_list_devices_query", "tests/unit/test_repository_application.py::TestQueries::test_search_devices_query", "tests/unit/test_repository_application.py::TestQueryHandler::test_handle_get_device_query", "tests/unit/test_repository_application.py::TestQueryHandler::test_handle_get_nonexistent_device", "tests/unit/test_repository_application.py::TestQueryHandler::test_handle_list_devices_query", "tests/unit/test_repository_application.py::TestQueryValidation::test_validate_pagination", "tests/unit/test_repository_application.py::TestQueryValidation::test_validate_sort_parameters", "tests/unit/test_repository_domain.py::TestDeviceAggregate::test_device_aggregate_configuration", "tests/unit/test_repository_domain.py::TestDeviceAggregate::test_device_aggregate_creation", "tests/unit/test_repository_domain.py::TestDeviceAggregate::test_device_aggregate_record_metrics", "tests/unit/test_repository_domain.py::TestDeviceCapabilities::test_device_capabilities_creation", "tests/unit/test_repository_domain.py::TestDeviceCapabilities::test_device_capabilities_properties", "tests/unit/test_repository_domain.py::TestDeviceCapabilities::test_device_capabilities_required_protocols", "tests/unit/test_repository_domain.py::TestDeviceEntity::test_device_entity_creation", "tests/unit/test_repository_domain.py::TestDeviceEntity::test_device_entity_deactivate", "tests/unit/test_repository_domain.py::TestDeviceEntity::test_device_entity_is_online", "tests/unit/test_repository_domain.py::TestDeviceEntity::test_device_entity_update_name", "tests/unit/test_repository_domain.py::TestDeviceEntity::test_device_entity_validation", "tests/unit/test_repository_domain.py::TestDeviceId::test_device_id_creation", "tests/unit/test_repository_domain.py::TestDeviceId::test_device_id_from_string", "tests/unit/test_repository_domain.py::TestDeviceId::test_device_id_generate", "tests/unit/test_repository_domain.py::TestDeviceId::test_device_id_invalid_string", "tests/unit/test_repository_domain.py::TestDeviceIdentifier::test_device_identifier_creation", "tests/unit/test_repository_domain.py::TestDeviceIdentifier::test_device_identifier_invalid_mac", "tests/unit/test_repository_domain.py::TestDeviceIdentifier::test_device_identifier_required_serial", "tests/unit/test_repository_domain.py::TestDeviceIdentifier::test_device_identifier_valid_mac_formats", "tests/unit/test_repository_domain.py::TestDeviceLifecycleService::test_calculate_device_health_score", "tests/unit/test_repository_domain.py::TestDeviceLifecycleService::test_can_deactivate_device", "tests/unit/test_repository_domain.py::TestDeviceLocation::test_device_location_coordinates_validation", "tests/unit/test_repository_domain.py::TestDeviceLocation::test_device_location_creation", "tests/unit/test_repository_domain.py::TestDeviceLocation::test_device_location_properties", "tests/unit/test_repository_domain.py::TestDeviceMetrics::test_device_metrics_create_now", "tests/unit/test_repository_domain.py::TestDeviceMetrics::test_device_metrics_creation", "tests/unit/test_repository_domain.py::TestDeviceMetrics::test_device_metrics_to_dict", "tests/unit/test_repository_domain.py::TestDeviceMetrics::test_device_metrics_validation", "tests/unit/test_repository_domain.py::TestDeviceValidationService::test_validate_device_name", "tests/unit/test_repository_domain.py::TestDeviceValidationService::test_validate_device_type_compatibility", "tests/unit/test_repository_domain.py::TestDeviceValidationService::test_validate_serial_number", "tests/unit/test_repository_infrastructure.py::TestDatabaseSession::test_create_and_drop_tables", "tests/unit/test_repository_infrastructure.py::TestDatabaseSession::test_create_test_database", "tests/unit/test_repository_infrastructure.py::TestDatabaseSession::test_database_migration", "tests/unit/test_repository_infrastructure.py::TestDatabaseSession::test_session_context_manager", "tests/unit/test_repository_infrastructure.py::TestEventStoreIntegration::test_event_ordering", "tests/unit/test_repository_infrastructure.py::TestEventStoreIntegration::test_event_replay_scenario", "tests/unit/test_repository_infrastructure.py::TestEventStoreIntegration::test_multiple_aggregates", "tests/unit/test_repository_infrastructure.py::TestInMemoryDeviceRepository::test_clear", "tests/unit/test_repository_infrastructure.py::TestInMemoryDeviceRepository::test_delete_device", "tests/unit/test_repository_infrastructure.py::TestInMemoryDeviceRepository::test_find_by_criteria", "tests/unit/test_repository_infrastructure.py::TestInMemoryDeviceRepository::test_get_all_devices", "tests/unit/test_repository_infrastructure.py::TestInMemoryDeviceRepository::test_get_by_serial_number", "tests/unit/test_repository_infrastructure.py::TestInMemoryDeviceRepository::test_save_and_get_device", "tests/unit/test_repository_infrastructure.py::TestInMemoryEventStore::test_clear", "tests/unit/test_repository_infrastructure.py::TestInMemoryEventStore::test_concurrency_conflict", "tests/unit/test_repository_infrastructure.py::TestInMemoryEventStore::test_get_all_events", "tests/unit/test_repository_infrastructure.py::TestInMemoryEventStore::test_get_events_by_type", "tests/unit/test_repository_infrastructure.py::TestInMemoryEventStore::test_get_events_from_version", "tests/unit/test_repository_infrastructure.py::TestInMemoryEventStore::test_save_and_get_events", "tests/unit/test_repository_infrastructure.py::TestInMemoryUnitOfWork::test_clear", "tests/unit/test_repository_infrastructure.py::TestInMemoryUnitOfWork::test_collect_new_events", "tests/unit/test_repository_infrastructure.py::TestInMemoryUnitOfWork::test_unit_of_work_commit", "tests/unit/test_repository_infrastructure.py::TestInMemoryUnitOfWork::test_unit_of_work_context_manager", "tests/unit/test_repository_infrastructure.py::TestInMemoryUnitOfWork::test_unit_of_work_rollback"]
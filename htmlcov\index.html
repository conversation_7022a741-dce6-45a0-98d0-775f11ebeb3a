<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">37%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-07-16 17:46 +0530
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_73bda9688a204f7a___init___py.html">edge_device_fleet_manager\__init__.py</a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c___init___py.html">edge_device_fleet_manager\cli\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html">edge_device_fleet_manager\cli\main.py</a></td>
                <td>214</td>
                <td>156</td>
                <td>2</td>
                <td class="right" data-ratio="58 214">27%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html">edge_device_fleet_manager\cli\types.py</a></td>
                <td>136</td>
                <td>136</td>
                <td>0</td>
                <td class="right" data-ratio="0 136">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html">edge_device_fleet_manager\core\config.py</a></td>
                <td>271</td>
                <td>159</td>
                <td>0</td>
                <td class="right" data-ratio="112 271">41%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html">edge_device_fleet_manager\core\context.py</a></td>
                <td>169</td>
                <td>100</td>
                <td>0</td>
                <td class="right" data-ratio="69 169">41%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html">edge_device_fleet_manager\core\exceptions.py</a></td>
                <td>35</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="35 35">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html">edge_device_fleet_manager\core\logging.py</a></td>
                <td>125</td>
                <td>82</td>
                <td>0</td>
                <td class="right" data-ratio="43 125">34%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html">edge_device_fleet_manager\core\plugins.py</a></td>
                <td>215</td>
                <td>164</td>
                <td>0</td>
                <td class="right" data-ratio="51 215">24%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d___init___py.html">edge_device_fleet_manager\discovery\__init__.py</a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html">edge_device_fleet_manager\discovery\cache.py</a></td>
                <td>200</td>
                <td>159</td>
                <td>0</td>
                <td class="right" data-ratio="41 200">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html">edge_device_fleet_manager\discovery\core.py</a></td>
                <td>176</td>
                <td>99</td>
                <td>8</td>
                <td class="right" data-ratio="77 176">44%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_exceptions_py.html">edge_device_fleet_manager\discovery\exceptions.py</a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e___init___py.html">edge_device_fleet_manager\discovery\protocols\__init__.py</a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_mdns_py.html">edge_device_fleet_manager\discovery\protocols\mdns.py</a></td>
                <td>283</td>
                <td>249</td>
                <td>0</td>
                <td class="right" data-ratio="34 283">12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_network_scan_py.html">edge_device_fleet_manager\discovery\protocols\network_scan.py</a></td>
                <td>232</td>
                <td>195</td>
                <td>0</td>
                <td class="right" data-ratio="37 232">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_ssdp_py.html">edge_device_fleet_manager\discovery\protocols\ssdp.py</a></td>
                <td>208</td>
                <td>176</td>
                <td>0</td>
                <td class="right" data-ratio="32 208">15%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_rate_limiter_py.html">edge_device_fleet_manager\discovery\rate_limiter.py</a></td>
                <td>117</td>
                <td>81</td>
                <td>0</td>
                <td class="right" data-ratio="36 117">31%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_50d91cd27b284a0f___init___py.html">edge_device_fleet_manager\plugins\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_50d91cd27b284a0f_sample_plugin_py.html">edge_device_fleet_manager\plugins\sample_plugin.py</a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_17de617221a3d7e6___init___py.html">edge_device_fleet_manager\repository\__init__.py</a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233___init___py.html">edge_device_fleet_manager\repository\application\__init__.py</a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td>190</td>
                <td>88</td>
                <td>5</td>
                <td class="right" data-ratio="102 190">54%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_dto_py.html">edge_device_fleet_manager\repository\application\dto.py</a></td>
                <td>189</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="154 189">81%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html">edge_device_fleet_manager\repository\application\handlers.py</a></td>
                <td>252</td>
                <td>210</td>
                <td>8</td>
                <td class="right" data-ratio="42 252">17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html">edge_device_fleet_manager\repository\application\queries.py</a></td>
                <td>218</td>
                <td>85</td>
                <td>5</td>
                <td class="right" data-ratio="133 218">61%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_services_py.html">edge_device_fleet_manager\repository\application\services.py</a></td>
                <td>101</td>
                <td>70</td>
                <td>0</td>
                <td class="right" data-ratio="31 101">31%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79___init___py.html">edge_device_fleet_manager\repository\domain\__init__.py</a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html">edge_device_fleet_manager\repository\domain\entities.py</a></td>
                <td>222</td>
                <td>66</td>
                <td>0</td>
                <td class="right" data-ratio="156 222">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html">edge_device_fleet_manager\repository\domain\events.py</a></td>
                <td>108</td>
                <td>26</td>
                <td>9</td>
                <td class="right" data-ratio="82 108">76%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_services_py.html">edge_device_fleet_manager\repository\domain\services.py</a></td>
                <td>147</td>
                <td>77</td>
                <td>0</td>
                <td class="right" data-ratio="70 147">48%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html">edge_device_fleet_manager\repository\domain\value_objects.py</a></td>
                <td>184</td>
                <td>50</td>
                <td>0</td>
                <td class="right" data-ratio="134 184">73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7___init___py.html">edge_device_fleet_manager\repository\infrastructure\__init__.py</a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_database_py.html">edge_device_fleet_manager\repository\infrastructure\database.py</a></td>
                <td>123</td>
                <td>93</td>
                <td>0</td>
                <td class="right" data-ratio="30 123">24%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html">edge_device_fleet_manager\repository\infrastructure\event_store.py</a></td>
                <td>123</td>
                <td>82</td>
                <td>18</td>
                <td class="right" data-ratio="41 123">33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html">edge_device_fleet_manager\repository\infrastructure\repositories.py</a></td>
                <td>215</td>
                <td>145</td>
                <td>44</td>
                <td class="right" data-ratio="70 215">33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html">edge_device_fleet_manager\repository\infrastructure\unit_of_work.py</a></td>
                <td>152</td>
                <td>103</td>
                <td>24</td>
                <td class="right" data-ratio="49 152">32%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4e142099aadd2379___init___py.html">edge_device_fleet_manager\utils\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html">edge_device_fleet_manager\utils\decorators.py</a></td>
                <td>127</td>
                <td>127</td>
                <td>0</td>
                <td class="right" data-ratio="0 127">0%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>4811</td>
                <td>3038</td>
                <td>123</td>
                <td class="right" data-ratio="1773 4811">37%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-07-16 17:46 +0530
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="z_4e142099aadd2379_decorators_py.html"></a>
        <a id="nextFileLink" class="nav" href="z_73bda9688a204f7a___init___py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>

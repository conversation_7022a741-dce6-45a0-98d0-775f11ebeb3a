<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage for edge_device_fleet_manager\discovery\core.py: 44%</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="pyfile">
<header>
    <div class="content">
        <h1>
            <span class="text">Coverage for </span><b>edge_device_fleet_manager\discovery\core.py</b>:
            <span class="pc_cov">44%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>r</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        &nbsp; toggle line displays
                    </p>
                    <p>
                        <kbd>j</kbd>
                        <kbd>k</kbd>
                        &nbsp; next/prev highlighted chunk
                    </p>
                    <p>
                        <kbd>0</kbd> &nbsp; (zero) top of page
                    </p>
                    <p>
                        <kbd>1</kbd> &nbsp; (one) first highlighted chunk
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>u</kbd> &nbsp; up to the index
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <h2>
            <span class="text">176 statements &nbsp;</span>
            <button type="button" class="run button_toggle_run" value="run" data-shortcut="r" title="Toggle lines run">77<span class="text"> run</span></button>
            <button type="button" class="mis show_mis button_toggle_mis" value="mis" data-shortcut="m" title="Toggle lines missing">99<span class="text"> missing</span></button>
            <button type="button" class="exc show_exc button_toggle_exc" value="exc" data-shortcut="x" title="Toggle lines excluded">8<span class="text"> excluded</span></button>
        </h2>
        <p class="text">
            <a id="prevFileLink" class="nav" href="z_93be9be7fbc09c3d_cache_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a id="indexLink" class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a id="nextFileLink" class="nav" href="z_93be9be7fbc09c3d_exceptions_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-07-16 17:46 +0530
        </p>
        <aside class="hidden">
            <button type="button" class="button_next_chunk" data-shortcut="j"></button>
            <button type="button" class="button_prev_chunk" data-shortcut="k"></button>
            <button type="button" class="button_top_of_page" data-shortcut="0"></button>
            <button type="button" class="button_first_chunk" data-shortcut="1"></button>
            <button type="button" class="button_prev_file" data-shortcut="["></button>
            <button type="button" class="button_next_file" data-shortcut="]"></button>
            <button type="button" class="button_to_index" data-shortcut="u"></button>
            <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
        </aside>
    </div>
</header>
<main id="source">
    <p class="pln"><span class="n"><a id="t1" href="#t1">1</a></span><span class="t"><span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t2" href="#t2">2</a></span><span class="t"><span class="str">Core discovery system architecture.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t3" href="#t3">3</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t4" href="#t4">4</a></span><span class="t"><span class="str">This module defines the fundamental classes and interfaces for the device discovery system,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t5" href="#t5">5</a></span><span class="t"><span class="str">including device models, registry, and the main discovery engine.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t6" href="#t6">6</a></span><span class="t"><span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t7" href="#t7">7</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t8" href="#t8">8</a></span><span class="t"><span class="key">import</span> <span class="nam">asyncio</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t9" href="#t9">9</a></span><span class="t"><span class="key">import</span> <span class="nam">time</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t10" href="#t10">10</a></span><span class="t"><span class="key">from</span> <span class="nam">abc</span> <span class="key">import</span> <span class="nam">ABC</span><span class="op">,</span> <span class="nam">abstractmethod</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t11" href="#t11">11</a></span><span class="t"><span class="key">from</span> <span class="nam">dataclasses</span> <span class="key">import</span> <span class="nam">dataclass</span><span class="op">,</span> <span class="nam">field</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t12" href="#t12">12</a></span><span class="t"><span class="key">from</span> <span class="nam">datetime</span> <span class="key">import</span> <span class="nam">datetime</span><span class="op">,</span> <span class="nam">timezone</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t13" href="#t13">13</a></span><span class="t"><span class="key">from</span> <span class="nam">enum</span> <span class="key">import</span> <span class="nam">Enum</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t14" href="#t14">14</a></span><span class="t"><span class="key">from</span> <span class="nam">typing</span> <span class="key">import</span> <span class="nam">Dict</span><span class="op">,</span> <span class="nam">List</span><span class="op">,</span> <span class="nam">Optional</span><span class="op">,</span> <span class="nam">Set</span><span class="op">,</span> <span class="nam">Any</span><span class="op">,</span> <span class="nam">AsyncIterator</span><span class="op">,</span> <span class="nam">Callable</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t15" href="#t15">15</a></span><span class="t"><span class="key">from</span> <span class="nam">uuid</span> <span class="key">import</span> <span class="nam">uuid4</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t16" href="#t16">16</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t17" href="#t17">17</a></span><span class="t"><span class="key">from</span> <span class="op">.</span><span class="op">.</span><span class="nam">core</span><span class="op">.</span><span class="nam">logging</span> <span class="key">import</span> <span class="nam">get_logger</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t18" href="#t18">18</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t19" href="#t19">19</a></span><span class="t"><span class="nam">logger</span> <span class="op">=</span> <span class="nam">get_logger</span><span class="op">(</span><span class="nam">__name__</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t20" href="#t20">20</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t21" href="#t21">21</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t22" href="#t22">22</a></span><span class="t"><span class="key">class</span> <span class="nam">DeviceType</span><span class="op">(</span><span class="nam">Enum</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t23" href="#t23">23</a></span><span class="t">    <span class="str">"""Types of discoverable devices."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t24" href="#t24">24</a></span><span class="t">    <span class="nam">UNKNOWN</span> <span class="op">=</span> <span class="str">"unknown"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t25" href="#t25">25</a></span><span class="t">    <span class="nam">IOT_SENSOR</span> <span class="op">=</span> <span class="str">"iot_sensor"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t26" href="#t26">26</a></span><span class="t">    <span class="nam">IOT_GATEWAY</span> <span class="op">=</span> <span class="str">"iot_gateway"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t27" href="#t27">27</a></span><span class="t">    <span class="nam">CAMERA</span> <span class="op">=</span> <span class="str">"camera"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t28" href="#t28">28</a></span><span class="t">    <span class="nam">ROUTER</span> <span class="op">=</span> <span class="str">"router"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t29" href="#t29">29</a></span><span class="t">    <span class="nam">SWITCH</span> <span class="op">=</span> <span class="str">"switch"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t30" href="#t30">30</a></span><span class="t">    <span class="nam">ACCESS_POINT</span> <span class="op">=</span> <span class="str">"access_point"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t31" href="#t31">31</a></span><span class="t">    <span class="nam">PRINTER</span> <span class="op">=</span> <span class="str">"printer"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t32" href="#t32">32</a></span><span class="t">    <span class="nam">MEDIA_SERVER</span> <span class="op">=</span> <span class="str">"media_server"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t33" href="#t33">33</a></span><span class="t">    <span class="nam">SMART_HOME</span> <span class="op">=</span> <span class="str">"smart_home"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t34" href="#t34">34</a></span><span class="t">    <span class="nam">INDUSTRIAL</span> <span class="op">=</span> <span class="str">"industrial"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t35" href="#t35">35</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t36" href="#t36">36</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t37" href="#t37">37</a></span><span class="t"><span class="key">class</span> <span class="nam">DeviceStatus</span><span class="op">(</span><span class="nam">Enum</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t38" href="#t38">38</a></span><span class="t">    <span class="str">"""Device availability status."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t39" href="#t39">39</a></span><span class="t">    <span class="nam">ONLINE</span> <span class="op">=</span> <span class="str">"online"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t40" href="#t40">40</a></span><span class="t">    <span class="nam">OFFLINE</span> <span class="op">=</span> <span class="str">"offline"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t41" href="#t41">41</a></span><span class="t">    <span class="nam">UNKNOWN</span> <span class="op">=</span> <span class="str">"unknown"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t42" href="#t42">42</a></span><span class="t">    <span class="nam">UNREACHABLE</span> <span class="op">=</span> <span class="str">"unreachable"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t43" href="#t43">43</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t44" href="#t44">44</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t45" href="#t45">45</a></span><span class="t"><span class="op">@</span><span class="nam">dataclass</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t46" href="#t46">46</a></span><span class="t"><span class="key">class</span> <span class="nam">Device</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t47" href="#t47">47</a></span><span class="t">    <span class="str">"""Represents a discovered device."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t48" href="#t48">48</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t49" href="#t49">49</a></span><span class="t">    <span class="com"># Core identification</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t50" href="#t50">50</a></span><span class="t">    <span class="nam">device_id</span><span class="op">:</span> <span class="nam">str</span> <span class="op">=</span> <span class="nam">field</span><span class="op">(</span><span class="nam">default_factory</span><span class="op">=</span><span class="key">lambda</span><span class="op">:</span> <span class="nam">str</span><span class="op">(</span><span class="nam">uuid4</span><span class="op">(</span><span class="op">)</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t51" href="#t51">51</a></span><span class="t">    <span class="nam">name</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t52" href="#t52">52</a></span><span class="t">    <span class="nam">device_type</span><span class="op">:</span> <span class="nam">DeviceType</span> <span class="op">=</span> <span class="nam">DeviceType</span><span class="op">.</span><span class="nam">UNKNOWN</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t53" href="#t53">53</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t54" href="#t54">54</a></span><span class="t">    <span class="com"># Network information</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t55" href="#t55">55</a></span><span class="t">    <span class="nam">ip_address</span><span class="op">:</span> <span class="nam">str</span> <span class="op">=</span> <span class="str">""</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t56" href="#t56">56</a></span><span class="t">    <span class="nam">mac_address</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t57" href="#t57">57</a></span><span class="t">    <span class="nam">hostname</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t58" href="#t58">58</a></span><span class="t">    <span class="nam">ports</span><span class="op">:</span> <span class="nam">List</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span> <span class="op">=</span> <span class="nam">field</span><span class="op">(</span><span class="nam">default_factory</span><span class="op">=</span><span class="nam">list</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t59" href="#t59">59</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t60" href="#t60">60</a></span><span class="t">    <span class="com"># Discovery metadata</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t61" href="#t61">61</a></span><span class="t">    <span class="nam">discovery_protocol</span><span class="op">:</span> <span class="nam">str</span> <span class="op">=</span> <span class="str">""</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t62" href="#t62">62</a></span><span class="t">    <span class="nam">discovery_time</span><span class="op">:</span> <span class="nam">datetime</span> <span class="op">=</span> <span class="nam">field</span><span class="op">(</span><span class="nam">default_factory</span><span class="op">=</span><span class="key">lambda</span><span class="op">:</span> <span class="nam">datetime</span><span class="op">.</span><span class="nam">now</span><span class="op">(</span><span class="nam">timezone</span><span class="op">.</span><span class="nam">utc</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t63" href="#t63">63</a></span><span class="t">    <span class="nam">last_seen</span><span class="op">:</span> <span class="nam">datetime</span> <span class="op">=</span> <span class="nam">field</span><span class="op">(</span><span class="nam">default_factory</span><span class="op">=</span><span class="key">lambda</span><span class="op">:</span> <span class="nam">datetime</span><span class="op">.</span><span class="nam">now</span><span class="op">(</span><span class="nam">timezone</span><span class="op">.</span><span class="nam">utc</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t64" href="#t64">64</a></span><span class="t">    <span class="nam">status</span><span class="op">:</span> <span class="nam">DeviceStatus</span> <span class="op">=</span> <span class="nam">DeviceStatus</span><span class="op">.</span><span class="nam">UNKNOWN</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t65" href="#t65">65</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t66" href="#t66">66</a></span><span class="t">    <span class="com"># Device details</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t67" href="#t67">67</a></span><span class="t">    <span class="nam">manufacturer</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t68" href="#t68">68</a></span><span class="t">    <span class="nam">model</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t69" href="#t69">69</a></span><span class="t">    <span class="nam">firmware_version</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t70" href="#t70">70</a></span><span class="t">    <span class="nam">services</span><span class="op">:</span> <span class="nam">List</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">field</span><span class="op">(</span><span class="nam">default_factory</span><span class="op">=</span><span class="nam">list</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t71" href="#t71">71</a></span><span class="t">    <span class="nam">capabilities</span><span class="op">:</span> <span class="nam">Dict</span><span class="op">[</span><span class="nam">str</span><span class="op">,</span> <span class="nam">Any</span><span class="op">]</span> <span class="op">=</span> <span class="nam">field</span><span class="op">(</span><span class="nam">default_factory</span><span class="op">=</span><span class="nam">dict</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t72" href="#t72">72</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t73" href="#t73">73</a></span><span class="t">    <span class="com"># Additional metadata</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t74" href="#t74">74</a></span><span class="t">    <span class="nam">metadata</span><span class="op">:</span> <span class="nam">Dict</span><span class="op">[</span><span class="nam">str</span><span class="op">,</span> <span class="nam">Any</span><span class="op">]</span> <span class="op">=</span> <span class="nam">field</span><span class="op">(</span><span class="nam">default_factory</span><span class="op">=</span><span class="nam">dict</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t75" href="#t75">75</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t76" href="#t76">76</a></span><span class="t">    <span class="key">def</span> <span class="nam">update_last_seen</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span> <span class="op">-></span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t77" href="#t77">77</a></span><span class="t">        <span class="str">"""Update the last seen timestamp."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t78" href="#t78">78</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">last_seen</span> <span class="op">=</span> <span class="nam">datetime</span><span class="op">.</span><span class="nam">now</span><span class="op">(</span><span class="nam">timezone</span><span class="op">.</span><span class="nam">utc</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t79" href="#t79">79</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">status</span> <span class="op">=</span> <span class="nam">DeviceStatus</span><span class="op">.</span><span class="nam">ONLINE</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t80" href="#t80">80</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t81" href="#t81">81</a></span><span class="t">    <span class="key">def</span> <span class="nam">is_stale</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">ttl_seconds</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="num">300</span><span class="op">)</span> <span class="op">-></span> <span class="nam">bool</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t82" href="#t82">82</a></span><span class="t">        <span class="str">"""Check if device information is stale."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t83" href="#t83">83</a></span><span class="t">        <span class="nam">age</span> <span class="op">=</span> <span class="op">(</span><span class="nam">datetime</span><span class="op">.</span><span class="nam">now</span><span class="op">(</span><span class="nam">timezone</span><span class="op">.</span><span class="nam">utc</span><span class="op">)</span> <span class="op">-</span> <span class="nam">self</span><span class="op">.</span><span class="nam">last_seen</span><span class="op">)</span><span class="op">.</span><span class="nam">total_seconds</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t84" href="#t84">84</a></span><span class="t">        <span class="key">return</span> <span class="nam">age</span> <span class="op">></span> <span class="nam">ttl_seconds</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t85" href="#t85">85</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t86" href="#t86">86</a></span><span class="t">    <span class="key">def</span> <span class="nam">to_dict</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span> <span class="op">-></span> <span class="nam">Dict</span><span class="op">[</span><span class="nam">str</span><span class="op">,</span> <span class="nam">Any</span><span class="op">]</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t87" href="#t87">87</a></span><span class="t">        <span class="str">"""Convert device to dictionary representation."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t88" href="#t88">88</a></span><span class="t">        <span class="key">return</span> <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t89" href="#t89">89</a></span><span class="t">            <span class="str">"device_id"</span><span class="op">:</span> <span class="nam">self</span><span class="op">.</span><span class="nam">device_id</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t90" href="#t90">90</a></span><span class="t">            <span class="str">"name"</span><span class="op">:</span> <span class="nam">self</span><span class="op">.</span><span class="nam">name</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t91" href="#t91">91</a></span><span class="t">            <span class="str">"device_type"</span><span class="op">:</span> <span class="nam">self</span><span class="op">.</span><span class="nam">device_type</span><span class="op">.</span><span class="nam">value</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t92" href="#t92">92</a></span><span class="t">            <span class="str">"ip_address"</span><span class="op">:</span> <span class="nam">self</span><span class="op">.</span><span class="nam">ip_address</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t93" href="#t93">93</a></span><span class="t">            <span class="str">"mac_address"</span><span class="op">:</span> <span class="nam">self</span><span class="op">.</span><span class="nam">mac_address</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t94" href="#t94">94</a></span><span class="t">            <span class="str">"hostname"</span><span class="op">:</span> <span class="nam">self</span><span class="op">.</span><span class="nam">hostname</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t95" href="#t95">95</a></span><span class="t">            <span class="str">"ports"</span><span class="op">:</span> <span class="nam">self</span><span class="op">.</span><span class="nam">ports</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t96" href="#t96">96</a></span><span class="t">            <span class="str">"discovery_protocol"</span><span class="op">:</span> <span class="nam">self</span><span class="op">.</span><span class="nam">discovery_protocol</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t97" href="#t97">97</a></span><span class="t">            <span class="str">"discovery_time"</span><span class="op">:</span> <span class="nam">self</span><span class="op">.</span><span class="nam">discovery_time</span><span class="op">.</span><span class="nam">isoformat</span><span class="op">(</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t98" href="#t98">98</a></span><span class="t">            <span class="str">"last_seen"</span><span class="op">:</span> <span class="nam">self</span><span class="op">.</span><span class="nam">last_seen</span><span class="op">.</span><span class="nam">isoformat</span><span class="op">(</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t99" href="#t99">99</a></span><span class="t">            <span class="str">"status"</span><span class="op">:</span> <span class="nam">self</span><span class="op">.</span><span class="nam">status</span><span class="op">.</span><span class="nam">value</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t100" href="#t100">100</a></span><span class="t">            <span class="str">"manufacturer"</span><span class="op">:</span> <span class="nam">self</span><span class="op">.</span><span class="nam">manufacturer</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t101" href="#t101">101</a></span><span class="t">            <span class="str">"model"</span><span class="op">:</span> <span class="nam">self</span><span class="op">.</span><span class="nam">model</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t102" href="#t102">102</a></span><span class="t">            <span class="str">"firmware_version"</span><span class="op">:</span> <span class="nam">self</span><span class="op">.</span><span class="nam">firmware_version</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t103" href="#t103">103</a></span><span class="t">            <span class="str">"services"</span><span class="op">:</span> <span class="nam">self</span><span class="op">.</span><span class="nam">services</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t104" href="#t104">104</a></span><span class="t">            <span class="str">"capabilities"</span><span class="op">:</span> <span class="nam">self</span><span class="op">.</span><span class="nam">capabilities</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t105" href="#t105">105</a></span><span class="t">            <span class="str">"metadata"</span><span class="op">:</span> <span class="nam">self</span><span class="op">.</span><span class="nam">metadata</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t106" href="#t106">106</a></span><span class="t">        <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t107" href="#t107">107</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t108" href="#t108">108</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t109" href="#t109">109</a></span><span class="t"><span class="op">@</span><span class="nam">dataclass</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t110" href="#t110">110</a></span><span class="t"><span class="key">class</span> <span class="nam">DiscoveryResult</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t111" href="#t111">111</a></span><span class="t">    <span class="str">"""Result of a discovery operation."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t112" href="#t112">112</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t113" href="#t113">113</a></span><span class="t">    <span class="nam">devices</span><span class="op">:</span> <span class="nam">List</span><span class="op">[</span><span class="nam">Device</span><span class="op">]</span> <span class="op">=</span> <span class="nam">field</span><span class="op">(</span><span class="nam">default_factory</span><span class="op">=</span><span class="nam">list</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t114" href="#t114">114</a></span><span class="t">    <span class="nam">protocol</span><span class="op">:</span> <span class="nam">str</span> <span class="op">=</span> <span class="str">""</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t115" href="#t115">115</a></span><span class="t">    <span class="nam">duration</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="num">0.0</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t116" href="#t116">116</a></span><span class="t">    <span class="nam">success</span><span class="op">:</span> <span class="nam">bool</span> <span class="op">=</span> <span class="key">True</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t117" href="#t117">117</a></span><span class="t">    <span class="nam">error</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t118" href="#t118">118</a></span><span class="t">    <span class="nam">metadata</span><span class="op">:</span> <span class="nam">Dict</span><span class="op">[</span><span class="nam">str</span><span class="op">,</span> <span class="nam">Any</span><span class="op">]</span> <span class="op">=</span> <span class="nam">field</span><span class="op">(</span><span class="nam">default_factory</span><span class="op">=</span><span class="nam">dict</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t119" href="#t119">119</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t120" href="#t120">120</a></span><span class="t">    <span class="key">def</span> <span class="nam">add_device</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">device</span><span class="op">:</span> <span class="nam">Device</span><span class="op">)</span> <span class="op">-></span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t121" href="#t121">121</a></span><span class="t">        <span class="str">"""Add a device to the result."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t122" href="#t122">122</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">devices</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="nam">device</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t123" href="#t123">123</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t124" href="#t124">124</a></span><span class="t">    <span class="key">def</span> <span class="nam">get_device_count</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span> <span class="op">-></span> <span class="nam">int</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t125" href="#t125">125</a></span><span class="t">        <span class="str">"""Get the number of discovered devices."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t126" href="#t126">126</a></span><span class="t">        <span class="key">return</span> <span class="nam">len</span><span class="op">(</span><span class="nam">self</span><span class="op">.</span><span class="nam">devices</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t127" href="#t127">127</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t128" href="#t128">128</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t129" href="#t129">129</a></span><span class="t"><span class="key">class</span> <span class="nam">DiscoveryProtocol</span><span class="op">(</span><span class="nam">ABC</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t130" href="#t130">130</a></span><span class="t">    <span class="str">"""Abstract base class for discovery protocols."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t131" href="#t131">131</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t132" href="#t132">132</a></span><span class="t">    <span class="key">def</span> <span class="nam">__init__</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">name</span><span class="op">:</span> <span class="nam">str</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t133" href="#t133">133</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">name</span> <span class="op">=</span> <span class="nam">name</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t134" href="#t134">134</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">logger</span> <span class="op">=</span> <span class="nam">get_logger</span><span class="op">(</span><span class="fst">f"</span><span class="op">{</span><span class="nam">__name__</span><span class="op">}</span><span class="fst">.</span><span class="op">{</span><span class="nam">name</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t135" href="#t135">135</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t136" href="#t136">136</a></span><span class="t">    <span class="op">@</span><span class="nam">abstractmethod</span>&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t137" href="#t137">137</a></span><span class="t">    <span class="key">async</span> <span class="key">def</span> <span class="nam">discover</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="op">**</span><span class="nam">kwargs</span><span class="op">)</span> <span class="op">-></span> <span class="nam">DiscoveryResult</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t138" href="#t138">138</a></span><span class="t">        <span class="str">"""Perform device discovery using this protocol."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t139" href="#t139">139</a></span><span class="t">        <span class="key">pass</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t140" href="#t140">140</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t141" href="#t141">141</a></span><span class="t">    <span class="op">@</span><span class="nam">abstractmethod</span>&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t142" href="#t142">142</a></span><span class="t">    <span class="key">async</span> <span class="key">def</span> <span class="nam">is_available</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span> <span class="op">-></span> <span class="nam">bool</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t143" href="#t143">143</a></span><span class="t">        <span class="str">"""Check if this protocol is available on the system."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t144" href="#t144">144</a></span><span class="t">        <span class="key">pass</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t145" href="#t145">145</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t146" href="#t146">146</a></span><span class="t">    <span class="key">def</span> <span class="nam">get_name</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span> <span class="op">-></span> <span class="nam">str</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t147" href="#t147">147</a></span><span class="t">        <span class="str">"""Get the protocol name."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t148" href="#t148">148</a></span><span class="t">        <span class="key">return</span> <span class="nam">self</span><span class="op">.</span><span class="nam">name</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t149" href="#t149">149</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t150" href="#t150">150</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t151" href="#t151">151</a></span><span class="t"><span class="key">class</span> <span class="nam">DeviceRegistry</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t152" href="#t152">152</a></span><span class="t">    <span class="str">"""Registry for managing discovered devices."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t153" href="#t153">153</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t154" href="#t154">154</a></span><span class="t">    <span class="key">def</span> <span class="nam">__init__</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t155" href="#t155">155</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">_devices</span><span class="op">:</span> <span class="nam">Dict</span><span class="op">[</span><span class="nam">str</span><span class="op">,</span> <span class="nam">Device</span><span class="op">]</span> <span class="op">=</span> <span class="op">{</span><span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t156" href="#t156">156</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">_ip_to_device</span><span class="op">:</span> <span class="nam">Dict</span><span class="op">[</span><span class="nam">str</span><span class="op">,</span> <span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="op">{</span><span class="op">}</span>  <span class="com"># IP -> device_id mapping</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t157" href="#t157">157</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">_lock</span> <span class="op">=</span> <span class="nam">asyncio</span><span class="op">.</span><span class="nam">Lock</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t158" href="#t158">158</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">logger</span> <span class="op">=</span> <span class="nam">get_logger</span><span class="op">(</span><span class="nam">__name__</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t159" href="#t159">159</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t160" href="#t160">160</a></span><span class="t">    <span class="key">async</span> <span class="key">def</span> <span class="nam">add_device</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">device</span><span class="op">:</span> <span class="nam">Device</span><span class="op">)</span> <span class="op">-></span> <span class="nam">bool</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t161" href="#t161">161</a></span><span class="t">        <span class="str">"""Add or update a device in the registry."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t162" href="#t162">162</a></span><span class="t">        <span class="key">async</span> <span class="key">with</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_lock</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t163" href="#t163">163</a></span><span class="t">            <span class="com"># Check if device already exists by IP</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t164" href="#t164">164</a></span><span class="t">            <span class="nam">existing_id</span> <span class="op">=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_ip_to_device</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="nam">device</span><span class="op">.</span><span class="nam">ip_address</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t165" href="#t165">165</a></span><span class="t">            <span class="key">if</span> <span class="nam">existing_id</span> <span class="key">and</span> <span class="nam">existing_id</span> <span class="key">in</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_devices</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t166" href="#t166">166</a></span><span class="t">                <span class="com"># Update existing device</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t167" href="#t167">167</a></span><span class="t">                <span class="nam">existing_device</span> <span class="op">=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_devices</span><span class="op">[</span><span class="nam">existing_id</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t168" href="#t168">168</a></span><span class="t">                <span class="nam">existing_device</span><span class="op">.</span><span class="nam">update_last_seen</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t169" href="#t169">169</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t170" href="#t170">170</a></span><span class="t">                <span class="com"># Merge information</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t171" href="#t171">171</a></span><span class="t">                <span class="key">if</span> <span class="nam">device</span><span class="op">.</span><span class="nam">name</span> <span class="key">and</span> <span class="key">not</span> <span class="nam">existing_device</span><span class="op">.</span><span class="nam">name</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t172" href="#t172">172</a></span><span class="t">                    <span class="nam">existing_device</span><span class="op">.</span><span class="nam">name</span> <span class="op">=</span> <span class="nam">device</span><span class="op">.</span><span class="nam">name</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t173" href="#t173">173</a></span><span class="t">                <span class="key">if</span> <span class="nam">device</span><span class="op">.</span><span class="nam">hostname</span> <span class="key">and</span> <span class="key">not</span> <span class="nam">existing_device</span><span class="op">.</span><span class="nam">hostname</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t174" href="#t174">174</a></span><span class="t">                    <span class="nam">existing_device</span><span class="op">.</span><span class="nam">hostname</span> <span class="op">=</span> <span class="nam">device</span><span class="op">.</span><span class="nam">hostname</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t175" href="#t175">175</a></span><span class="t">                <span class="key">if</span> <span class="nam">device</span><span class="op">.</span><span class="nam">mac_address</span> <span class="key">and</span> <span class="key">not</span> <span class="nam">existing_device</span><span class="op">.</span><span class="nam">mac_address</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t176" href="#t176">176</a></span><span class="t">                    <span class="nam">existing_device</span><span class="op">.</span><span class="nam">mac_address</span> <span class="op">=</span> <span class="nam">device</span><span class="op">.</span><span class="nam">mac_address</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t177" href="#t177">177</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t178" href="#t178">178</a></span><span class="t">                <span class="com"># Merge services and ports</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t179" href="#t179">179</a></span><span class="t">                <span class="nam">existing_device</span><span class="op">.</span><span class="nam">services</span> <span class="op">=</span> <span class="nam">list</span><span class="op">(</span><span class="nam">set</span><span class="op">(</span><span class="nam">existing_device</span><span class="op">.</span><span class="nam">services</span> <span class="op">+</span> <span class="nam">device</span><span class="op">.</span><span class="nam">services</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t180" href="#t180">180</a></span><span class="t">                <span class="nam">existing_device</span><span class="op">.</span><span class="nam">ports</span> <span class="op">=</span> <span class="nam">list</span><span class="op">(</span><span class="nam">set</span><span class="op">(</span><span class="nam">existing_device</span><span class="op">.</span><span class="nam">ports</span> <span class="op">+</span> <span class="nam">device</span><span class="op">.</span><span class="nam">ports</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t181" href="#t181">181</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t182" href="#t182">182</a></span><span class="t">                <span class="com"># Update capabilities and metadata</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t183" href="#t183">183</a></span><span class="t">                <span class="nam">existing_device</span><span class="op">.</span><span class="nam">capabilities</span><span class="op">.</span><span class="nam">update</span><span class="op">(</span><span class="nam">device</span><span class="op">.</span><span class="nam">capabilities</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t184" href="#t184">184</a></span><span class="t">                <span class="nam">existing_device</span><span class="op">.</span><span class="nam">metadata</span><span class="op">.</span><span class="nam">update</span><span class="op">(</span><span class="nam">device</span><span class="op">.</span><span class="nam">metadata</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t185" href="#t185">185</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t186" href="#t186">186</a></span><span class="t">                <span class="nam">self</span><span class="op">.</span><span class="nam">logger</span><span class="op">.</span><span class="nam">debug</span><span class="op">(</span><span class="str">"Updated existing device"</span><span class="op">,</span> <span class="nam">device_id</span><span class="op">=</span><span class="nam">existing_id</span><span class="op">,</span> <span class="nam">ip</span><span class="op">=</span><span class="nam">device</span><span class="op">.</span><span class="nam">ip_address</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t187" href="#t187">187</a></span><span class="t">                <span class="key">return</span> <span class="key">False</span>  <span class="com"># Not a new device</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t188" href="#t188">188</a></span><span class="t">            <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t189" href="#t189">189</a></span><span class="t">                <span class="com"># Add new device</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t190" href="#t190">190</a></span><span class="t">                <span class="nam">self</span><span class="op">.</span><span class="nam">_devices</span><span class="op">[</span><span class="nam">device</span><span class="op">.</span><span class="nam">device_id</span><span class="op">]</span> <span class="op">=</span> <span class="nam">device</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t191" href="#t191">191</a></span><span class="t">                <span class="nam">self</span><span class="op">.</span><span class="nam">_ip_to_device</span><span class="op">[</span><span class="nam">device</span><span class="op">.</span><span class="nam">ip_address</span><span class="op">]</span> <span class="op">=</span> <span class="nam">device</span><span class="op">.</span><span class="nam">device_id</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t192" href="#t192">192</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t193" href="#t193">193</a></span><span class="t">                <span class="nam">self</span><span class="op">.</span><span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"Added new device"</span><span class="op">,</span> <span class="nam">device_id</span><span class="op">=</span><span class="nam">device</span><span class="op">.</span><span class="nam">device_id</span><span class="op">,</span> <span class="nam">ip</span><span class="op">=</span><span class="nam">device</span><span class="op">.</span><span class="nam">ip_address</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t194" href="#t194">194</a></span><span class="t">                <span class="key">return</span> <span class="key">True</span>  <span class="com"># New device added</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t195" href="#t195">195</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t196" href="#t196">196</a></span><span class="t">    <span class="key">async</span> <span class="key">def</span> <span class="nam">get_device</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">device_id</span><span class="op">:</span> <span class="nam">str</span><span class="op">)</span> <span class="op">-></span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">Device</span><span class="op">]</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t197" href="#t197">197</a></span><span class="t">        <span class="str">"""Get a device by ID."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t198" href="#t198">198</a></span><span class="t">        <span class="key">async</span> <span class="key">with</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_lock</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t199" href="#t199">199</a></span><span class="t">            <span class="key">return</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_devices</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="nam">device_id</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t200" href="#t200">200</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t201" href="#t201">201</a></span><span class="t">    <span class="key">async</span> <span class="key">def</span> <span class="nam">get_device_by_ip</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">ip_address</span><span class="op">:</span> <span class="nam">str</span><span class="op">)</span> <span class="op">-></span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">Device</span><span class="op">]</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t202" href="#t202">202</a></span><span class="t">        <span class="str">"""Get a device by IP address."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t203" href="#t203">203</a></span><span class="t">        <span class="key">async</span> <span class="key">with</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_lock</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t204" href="#t204">204</a></span><span class="t">            <span class="nam">device_id</span> <span class="op">=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_ip_to_device</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="nam">ip_address</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t205" href="#t205">205</a></span><span class="t">            <span class="key">if</span> <span class="nam">device_id</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t206" href="#t206">206</a></span><span class="t">                <span class="key">return</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_devices</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="nam">device_id</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t207" href="#t207">207</a></span><span class="t">            <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t208" href="#t208">208</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t209" href="#t209">209</a></span><span class="t">    <span class="key">async</span> <span class="key">def</span> <span class="nam">get_all_devices</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span> <span class="op">-></span> <span class="nam">List</span><span class="op">[</span><span class="nam">Device</span><span class="op">]</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t210" href="#t210">210</a></span><span class="t">        <span class="str">"""Get all devices in the registry."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t211" href="#t211">211</a></span><span class="t">        <span class="key">async</span> <span class="key">with</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_lock</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t212" href="#t212">212</a></span><span class="t">            <span class="key">return</span> <span class="nam">list</span><span class="op">(</span><span class="nam">self</span><span class="op">.</span><span class="nam">_devices</span><span class="op">.</span><span class="nam">values</span><span class="op">(</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t213" href="#t213">213</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t214" href="#t214">214</a></span><span class="t">    <span class="key">async</span> <span class="key">def</span> <span class="nam">remove_device</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">device_id</span><span class="op">:</span> <span class="nam">str</span><span class="op">)</span> <span class="op">-></span> <span class="nam">bool</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t215" href="#t215">215</a></span><span class="t">        <span class="str">"""Remove a device from the registry."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t216" href="#t216">216</a></span><span class="t">        <span class="key">async</span> <span class="key">with</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_lock</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t217" href="#t217">217</a></span><span class="t">            <span class="nam">device</span> <span class="op">=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_devices</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="nam">device_id</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t218" href="#t218">218</a></span><span class="t">            <span class="key">if</span> <span class="nam">device</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t219" href="#t219">219</a></span><span class="t">                <span class="key">del</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_devices</span><span class="op">[</span><span class="nam">device_id</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t220" href="#t220">220</a></span><span class="t">                <span class="key">if</span> <span class="nam">device</span><span class="op">.</span><span class="nam">ip_address</span> <span class="key">in</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_ip_to_device</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t221" href="#t221">221</a></span><span class="t">                    <span class="key">del</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_ip_to_device</span><span class="op">[</span><span class="nam">device</span><span class="op">.</span><span class="nam">ip_address</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t222" href="#t222">222</a></span><span class="t">                <span class="nam">self</span><span class="op">.</span><span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"Removed device"</span><span class="op">,</span> <span class="nam">device_id</span><span class="op">=</span><span class="nam">device_id</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t223" href="#t223">223</a></span><span class="t">                <span class="key">return</span> <span class="key">True</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t224" href="#t224">224</a></span><span class="t">            <span class="key">return</span> <span class="key">False</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t225" href="#t225">225</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t226" href="#t226">226</a></span><span class="t">    <span class="key">async</span> <span class="key">def</span> <span class="nam">cleanup_stale_devices</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">ttl_seconds</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="num">300</span><span class="op">)</span> <span class="op">-></span> <span class="nam">int</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t227" href="#t227">227</a></span><span class="t">        <span class="str">"""Remove stale devices from the registry."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t228" href="#t228">228</a></span><span class="t">        <span class="key">async</span> <span class="key">with</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_lock</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t229" href="#t229">229</a></span><span class="t">            <span class="nam">stale_devices</span> <span class="op">=</span> <span class="op">[</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t230" href="#t230">230</a></span><span class="t">                <span class="nam">device_id</span> <span class="key">for</span> <span class="nam">device_id</span><span class="op">,</span> <span class="nam">device</span> <span class="key">in</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_devices</span><span class="op">.</span><span class="nam">items</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t231" href="#t231">231</a></span><span class="t">                <span class="key">if</span> <span class="nam">device</span><span class="op">.</span><span class="nam">is_stale</span><span class="op">(</span><span class="nam">ttl_seconds</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t232" href="#t232">232</a></span><span class="t">            <span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t233" href="#t233">233</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t234" href="#t234">234</a></span><span class="t">            <span class="key">for</span> <span class="nam">device_id</span> <span class="key">in</span> <span class="nam">stale_devices</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t235" href="#t235">235</a></span><span class="t">                <span class="nam">device</span> <span class="op">=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_devices</span><span class="op">[</span><span class="nam">device_id</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t236" href="#t236">236</a></span><span class="t">                <span class="key">del</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_devices</span><span class="op">[</span><span class="nam">device_id</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t237" href="#t237">237</a></span><span class="t">                <span class="key">if</span> <span class="nam">device</span><span class="op">.</span><span class="nam">ip_address</span> <span class="key">in</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_ip_to_device</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t238" href="#t238">238</a></span><span class="t">                    <span class="key">del</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_ip_to_device</span><span class="op">[</span><span class="nam">device</span><span class="op">.</span><span class="nam">ip_address</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t239" href="#t239">239</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t240" href="#t240">240</a></span><span class="t">            <span class="key">if</span> <span class="nam">stale_devices</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t241" href="#t241">241</a></span><span class="t">                <span class="nam">self</span><span class="op">.</span><span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"Cleaned up stale devices"</span><span class="op">,</span> <span class="nam">count</span><span class="op">=</span><span class="nam">len</span><span class="op">(</span><span class="nam">stale_devices</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t242" href="#t242">242</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t243" href="#t243">243</a></span><span class="t">            <span class="key">return</span> <span class="nam">len</span><span class="op">(</span><span class="nam">stale_devices</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t244" href="#t244">244</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t245" href="#t245">245</a></span><span class="t">    <span class="key">async</span> <span class="key">def</span> <span class="nam">get_device_count</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span> <span class="op">-></span> <span class="nam">int</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t246" href="#t246">246</a></span><span class="t">        <span class="str">"""Get the total number of devices."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t247" href="#t247">247</a></span><span class="t">        <span class="key">async</span> <span class="key">with</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_lock</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t248" href="#t248">248</a></span><span class="t">            <span class="key">return</span> <span class="nam">len</span><span class="op">(</span><span class="nam">self</span><span class="op">.</span><span class="nam">_devices</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t249" href="#t249">249</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t250" href="#t250">250</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t251" href="#t251">251</a></span><span class="t"><span class="key">class</span> <span class="nam">DiscoveryEngine</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t252" href="#t252">252</a></span><span class="t">    <span class="str">"""Main discovery engine that coordinates multiple protocols."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t253" href="#t253">253</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t254" href="#t254">254</a></span><span class="t">    <span class="key">def</span> <span class="nam">__init__</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">config</span><span class="op">,</span> <span class="nam">registry</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">DeviceRegistry</span><span class="op">]</span> <span class="op">=</span> <span class="key">None</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t255" href="#t255">255</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">config</span> <span class="op">=</span> <span class="nam">config</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t256" href="#t256">256</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">registry</span> <span class="op">=</span> <span class="nam">registry</span> <span class="key">or</span> <span class="nam">DeviceRegistry</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t257" href="#t257">257</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">protocols</span><span class="op">:</span> <span class="nam">Dict</span><span class="op">[</span><span class="nam">str</span><span class="op">,</span> <span class="nam">DiscoveryProtocol</span><span class="op">]</span> <span class="op">=</span> <span class="op">{</span><span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t258" href="#t258">258</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">logger</span> <span class="op">=</span> <span class="nam">get_logger</span><span class="op">(</span><span class="nam">__name__</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t259" href="#t259">259</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">_running</span> <span class="op">=</span> <span class="key">False</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t260" href="#t260">260</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">_discovery_tasks</span><span class="op">:</span> <span class="nam">Set</span><span class="op">[</span><span class="nam">asyncio</span><span class="op">.</span><span class="nam">Task</span><span class="op">]</span> <span class="op">=</span> <span class="nam">set</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t261" href="#t261">261</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t262" href="#t262">262</a></span><span class="t">    <span class="key">def</span> <span class="nam">register_protocol</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">protocol</span><span class="op">:</span> <span class="nam">DiscoveryProtocol</span><span class="op">)</span> <span class="op">-></span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t263" href="#t263">263</a></span><span class="t">        <span class="str">"""Register a discovery protocol."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t264" href="#t264">264</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">protocols</span><span class="op">[</span><span class="nam">protocol</span><span class="op">.</span><span class="nam">get_name</span><span class="op">(</span><span class="op">)</span><span class="op">]</span> <span class="op">=</span> <span class="nam">protocol</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t265" href="#t265">265</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"Registered discovery protocol"</span><span class="op">,</span> <span class="nam">protocol</span><span class="op">=</span><span class="nam">protocol</span><span class="op">.</span><span class="nam">get_name</span><span class="op">(</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t266" href="#t266">266</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t267" href="#t267">267</a></span><span class="t">    <span class="key">async</span> <span class="key">def</span> <span class="nam">discover_all</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">protocols</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">List</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span><span class="op">]</span> <span class="op">=</span> <span class="key">None</span><span class="op">)</span> <span class="op">-></span> <span class="nam">DiscoveryResult</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t268" href="#t268">268</a></span><span class="t">        <span class="str">"""Run discovery using all or specified protocols."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t269" href="#t269">269</a></span><span class="t">        <span class="nam">start_time</span> <span class="op">=</span> <span class="nam">time</span><span class="op">.</span><span class="nam">time</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t270" href="#t270">270</a></span><span class="t">        <span class="nam">result</span> <span class="op">=</span> <span class="nam">DiscoveryResult</span><span class="op">(</span><span class="nam">protocol</span><span class="op">=</span><span class="str">"all"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t271" href="#t271">271</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t272" href="#t272">272</a></span><span class="t">        <span class="com"># Use all protocols if none specified</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t273" href="#t273">273</a></span><span class="t">        <span class="key">if</span> <span class="nam">protocols</span> <span class="key">is</span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t274" href="#t274">274</a></span><span class="t">            <span class="nam">protocols</span> <span class="op">=</span> <span class="nam">list</span><span class="op">(</span><span class="nam">self</span><span class="op">.</span><span class="nam">protocols</span><span class="op">.</span><span class="nam">keys</span><span class="op">(</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t275" href="#t275">275</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t276" href="#t276">276</a></span><span class="t">        <span class="com"># Run discovery protocols concurrently</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t277" href="#t277">277</a></span><span class="t">        <span class="nam">tasks</span> <span class="op">=</span> <span class="op">[</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t278" href="#t278">278</a></span><span class="t">        <span class="key">for</span> <span class="nam">protocol_name</span> <span class="key">in</span> <span class="nam">protocols</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t279" href="#t279">279</a></span><span class="t">            <span class="key">if</span> <span class="nam">protocol_name</span> <span class="key">in</span> <span class="nam">self</span><span class="op">.</span><span class="nam">protocols</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t280" href="#t280">280</a></span><span class="t">                <span class="nam">protocol</span> <span class="op">=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">protocols</span><span class="op">[</span><span class="nam">protocol_name</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t281" href="#t281">281</a></span><span class="t">                <span class="nam">task</span> <span class="op">=</span> <span class="nam">asyncio</span><span class="op">.</span><span class="nam">create_task</span><span class="op">(</span><span class="nam">protocol</span><span class="op">.</span><span class="nam">discover</span><span class="op">(</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t282" href="#t282">282</a></span><span class="t">                <span class="nam">tasks</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="op">(</span><span class="nam">protocol_name</span><span class="op">,</span> <span class="nam">task</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t283" href="#t283">283</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t284" href="#t284">284</a></span><span class="t">        <span class="com"># Collect results</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t285" href="#t285">285</a></span><span class="t">        <span class="key">for</span> <span class="nam">protocol_name</span><span class="op">,</span> <span class="nam">task</span> <span class="key">in</span> <span class="nam">tasks</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t286" href="#t286">286</a></span><span class="t">            <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t287" href="#t287">287</a></span><span class="t">                <span class="nam">protocol_result</span> <span class="op">=</span> <span class="key">await</span> <span class="nam">task</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t288" href="#t288">288</a></span><span class="t">                <span class="nam">result</span><span class="op">.</span><span class="nam">devices</span><span class="op">.</span><span class="nam">extend</span><span class="op">(</span><span class="nam">protocol_result</span><span class="op">.</span><span class="nam">devices</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t289" href="#t289">289</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t290" href="#t290">290</a></span><span class="t">                <span class="com"># Add devices to registry</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t291" href="#t291">291</a></span><span class="t">                <span class="key">for</span> <span class="nam">device</span> <span class="key">in</span> <span class="nam">protocol_result</span><span class="op">.</span><span class="nam">devices</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t292" href="#t292">292</a></span><span class="t">                    <span class="key">await</span> <span class="nam">self</span><span class="op">.</span><span class="nam">registry</span><span class="op">.</span><span class="nam">add_device</span><span class="op">(</span><span class="nam">device</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t293" href="#t293">293</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t294" href="#t294">294</a></span><span class="t">                <span class="nam">self</span><span class="op">.</span><span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t295" href="#t295">295</a></span><span class="t">                    <span class="str">"Protocol discovery completed"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t296" href="#t296">296</a></span><span class="t">                    <span class="nam">protocol</span><span class="op">=</span><span class="nam">protocol_name</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t297" href="#t297">297</a></span><span class="t">                    <span class="nam">devices_found</span><span class="op">=</span><span class="nam">len</span><span class="op">(</span><span class="nam">protocol_result</span><span class="op">.</span><span class="nam">devices</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t298" href="#t298">298</a></span><span class="t">                    <span class="nam">duration</span><span class="op">=</span><span class="nam">protocol_result</span><span class="op">.</span><span class="nam">duration</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t299" href="#t299">299</a></span><span class="t">                <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t300" href="#t300">300</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t301" href="#t301">301</a></span><span class="t">            <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t302" href="#t302">302</a></span><span class="t">                <span class="nam">self</span><span class="op">.</span><span class="nam">logger</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t303" href="#t303">303</a></span><span class="t">                    <span class="str">"Protocol discovery failed"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t304" href="#t304">304</a></span><span class="t">                    <span class="nam">protocol</span><span class="op">=</span><span class="nam">protocol_name</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t305" href="#t305">305</a></span><span class="t">                    <span class="nam">error</span><span class="op">=</span><span class="nam">str</span><span class="op">(</span><span class="nam">e</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t306" href="#t306">306</a></span><span class="t">                    <span class="nam">exc_info</span><span class="op">=</span><span class="nam">e</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t307" href="#t307">307</a></span><span class="t">                <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t308" href="#t308">308</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t309" href="#t309">309</a></span><span class="t">        <span class="nam">result</span><span class="op">.</span><span class="nam">duration</span> <span class="op">=</span> <span class="nam">time</span><span class="op">.</span><span class="nam">time</span><span class="op">(</span><span class="op">)</span> <span class="op">-</span> <span class="nam">start_time</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t310" href="#t310">310</a></span><span class="t">        <span class="nam">result</span><span class="op">.</span><span class="nam">metadata</span><span class="op">[</span><span class="str">"total_protocols"</span><span class="op">]</span> <span class="op">=</span> <span class="nam">len</span><span class="op">(</span><span class="nam">protocols</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t311" href="#t311">311</a></span><span class="t">        <span class="nam">result</span><span class="op">.</span><span class="nam">metadata</span><span class="op">[</span><span class="str">"successful_protocols"</span><span class="op">]</span> <span class="op">=</span> <span class="nam">len</span><span class="op">(</span><span class="op">[</span><span class="nam">t</span> <span class="key">for</span> <span class="nam">_</span><span class="op">,</span> <span class="nam">t</span> <span class="key">in</span> <span class="nam">tasks</span> <span class="key">if</span> <span class="key">not</span> <span class="nam">t</span><span class="op">.</span><span class="nam">exception</span><span class="op">(</span><span class="op">)</span><span class="op">]</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t312" href="#t312">312</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t313" href="#t313">313</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t314" href="#t314">314</a></span><span class="t">            <span class="str">"Discovery completed"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t315" href="#t315">315</a></span><span class="t">            <span class="nam">total_devices</span><span class="op">=</span><span class="nam">len</span><span class="op">(</span><span class="nam">result</span><span class="op">.</span><span class="nam">devices</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t316" href="#t316">316</a></span><span class="t">            <span class="nam">duration</span><span class="op">=</span><span class="nam">result</span><span class="op">.</span><span class="nam">duration</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t317" href="#t317">317</a></span><span class="t">            <span class="nam">protocols</span><span class="op">=</span><span class="nam">len</span><span class="op">(</span><span class="nam">protocols</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t318" href="#t318">318</a></span><span class="t">        <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t319" href="#t319">319</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t320" href="#t320">320</a></span><span class="t">        <span class="key">return</span> <span class="nam">result</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t321" href="#t321">321</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t322" href="#t322">322</a></span><span class="t">    <span class="key">async</span> <span class="key">def</span> <span class="nam">get_devices</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span> <span class="op">-></span> <span class="nam">List</span><span class="op">[</span><span class="nam">Device</span><span class="op">]</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t323" href="#t323">323</a></span><span class="t">        <span class="str">"""Get all discovered devices."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t324" href="#t324">324</a></span><span class="t">        <span class="key">return</span> <span class="key">await</span> <span class="nam">self</span><span class="op">.</span><span class="nam">registry</span><span class="op">.</span><span class="nam">get_all_devices</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t325" href="#t325">325</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t326" href="#t326">326</a></span><span class="t">    <span class="key">async</span> <span class="key">def</span> <span class="nam">cleanup_stale_devices</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span> <span class="op">-></span> <span class="nam">int</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t327" href="#t327">327</a></span><span class="t">        <span class="str">"""Clean up stale devices."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t328" href="#t328">328</a></span><span class="t">        <span class="key">return</span> <span class="key">await</span> <span class="nam">self</span><span class="op">.</span><span class="nam">registry</span><span class="op">.</span><span class="nam">cleanup_stale_devices</span><span class="op">(</span><span class="nam">self</span><span class="op">.</span><span class="nam">config</span><span class="op">.</span><span class="nam">discovery</span><span class="op">.</span><span class="nam">cache_ttl</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="z_93be9be7fbc09c3d_cache_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a class="nav" href="z_93be9be7fbc09c3d_exceptions_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-07-16 17:46 +0530
        </p>
    </div>
</footer>
</body>
</html>

"""
Unit tests for discovery rate limiting functionality.

Tests the rate limiting system including TokenBucket, AdaptiveRateLimiter,
and RateLimiter components.
"""

import asyncio
import pytest
import time
from unittest.mock import patch

from edge_device_fleet_manager.discovery.rate_limiter import (
    TokenBucket, AdaptiveRateLimiter, RateLimiter, RateLimitConfig
)
from edge_device_fleet_manager.discovery.exceptions import RateLimitExceededError


class TestTokenBucket:
    """Test TokenBucket class."""
    
    def test_token_bucket_creation(self):
        """Test token bucket creation."""
        bucket = TokenBucket(rate=10.0, capacity=20)
        
        assert bucket.rate == 10.0
        assert bucket.capacity == 20
        assert bucket.tokens == 20  # Starts full
    
    async def test_consume_tokens(self):
        """Test token consumption."""
        bucket = TokenBucket(rate=10.0, capacity=20)
        
        # Should be able to consume tokens
        assert await bucket.consume(5) is True
        assert bucket.tokens == 15
        
        # Consume more tokens
        assert await bucket.consume(10) is True
        assert bucket.tokens == 5
        
        # Try to consume more than available
        assert await bucket.consume(10) is False
        assert bucket.tokens == 5  # Should remain unchanged
    
    async def test_token_refill(self):
        """Test token refill over time."""
        bucket = TokenBucket(rate=10.0, capacity=20)
        
        # Consume all tokens
        await bucket.consume(20)
        assert bucket.tokens == 0
        
        # Wait and check refill
        await asyncio.sleep(0.1)  # 0.1 seconds
        
        # Should have refilled some tokens (10 tokens/sec * 0.1 sec = 1 token)
        assert await bucket.consume(1) is True
    
    async def test_wait_for_tokens(self):
        """Test waiting for tokens."""
        bucket = TokenBucket(rate=10.0, capacity=10)
        
        # Consume all tokens
        await bucket.consume(10)
        
        # Wait for tokens (should succeed quickly due to refill)
        start_time = time.time()
        result = await bucket.wait_for_tokens(1, timeout=1.0)
        elapsed = time.time() - start_time
        
        assert result is True
        assert elapsed < 1.0  # Should not take the full timeout
    
    async def test_wait_for_tokens_timeout(self):
        """Test waiting for tokens with timeout."""
        bucket = TokenBucket(rate=1.0, capacity=1)  # Very slow refill
        
        # Consume all tokens
        await bucket.consume(1)
        
        # Wait for more tokens than can be refilled in time
        start_time = time.time()
        result = await bucket.wait_for_tokens(10, timeout=0.1)
        elapsed = time.time() - start_time
        
        assert result is False
        assert elapsed >= 0.1  # Should take at least the timeout


class TestRateLimitConfig:
    """Test RateLimitConfig class."""
    
    def test_default_config(self):
        """Test default configuration values."""
        config = RateLimitConfig()
        
        assert config.requests_per_second == 10.0
        assert config.burst_size == 20
        assert config.per_host_limit == 2.0
        assert config.global_limit == 100.0
        assert config.backoff_factor == 1.5
        assert config.max_backoff == 60.0
    
    def test_custom_config(self):
        """Test custom configuration values."""
        config = RateLimitConfig(
            requests_per_second=5.0,
            burst_size=10,
            per_host_limit=1.0,
            global_limit=50.0
        )
        
        assert config.requests_per_second == 5.0
        assert config.burst_size == 10
        assert config.per_host_limit == 1.0
        assert config.global_limit == 50.0


class TestAdaptiveRateLimiter:
    """Test AdaptiveRateLimiter class."""
    
    @pytest.fixture
    def config(self):
        """Create rate limit configuration."""
        return RateLimitConfig(
            requests_per_second=10.0,
            per_host_limit=2.0,
            global_limit=20.0,
            backoff_factor=2.0,
            max_backoff=10.0
        )
    
    @pytest.fixture
    def limiter(self, config):
        """Create adaptive rate limiter."""
        return AdaptiveRateLimiter(config)
    
    async def test_acquire_success(self, limiter):
        """Test successful request acquisition."""
        result = await limiter.acquire("test.example.com", timeout=1.0)
        assert result is True
    
    async def test_acquire_global_limit(self, limiter):
        """Test global rate limit enforcement."""
        # Exhaust global tokens
        for _ in range(40):  # More than global capacity
            try:
                await limiter.acquire("test.example.com", timeout=0.1)
            except RateLimitExceededError:
                break
        
        # Next request should fail
        with pytest.raises(RateLimitExceededError, match="Global rate limit exceeded"):
            await limiter.acquire("test.example.com", timeout=0.1)
    
    async def test_acquire_per_host_limit(self, limiter):
        """Test per-host rate limit enforcement."""
        host = "test.example.com"
        
        # Make requests to exhaust host bucket
        for _ in range(10):  # More than per-host capacity
            try:
                await limiter.acquire(host, timeout=0.1)
            except RateLimitExceededError:
                break
        
        # Next request should fail
        with pytest.raises(RateLimitExceededError, match=f"Rate limit exceeded for host {host}"):
            await limiter.acquire(host, timeout=0.1)
    
    def test_record_success(self, limiter):
        """Test recording successful requests."""
        host = "test.example.com"
        
        # Record success
        limiter.record_success(host, response_time=0.5)
        
        # Check stats
        stats = limiter.get_host_stats(host)
        assert stats["total_requests"] == 1
        assert stats["success_rate"] == 1.0
        assert stats["avg_response_time"] == 0.5
    
    def test_record_failure(self, limiter):
        """Test recording failed requests."""
        host = "test.example.com"
        
        # Record failure
        limiter.record_failure(host, error_type="timeout")
        
        # Check stats
        stats = limiter.get_host_stats(host)
        assert stats["total_requests"] == 1
        assert stats["success_rate"] == 0.0
        assert stats["current_backoff"] > 0
    
    def test_backoff_increase(self, limiter):
        """Test backoff increase on failures."""
        host = "test.example.com"
        
        # Record multiple failures
        limiter.record_failure(host, "error1")
        backoff1 = limiter.backoff_delays[host]
        
        limiter.record_failure(host, "error2")
        backoff2 = limiter.backoff_delays[host]
        
        # Backoff should increase
        assert backoff2 > backoff1
        assert backoff2 <= limiter.config.max_backoff
    
    def test_backoff_decrease(self, limiter):
        """Test backoff decrease on success."""
        host = "test.example.com"
        
        # Set initial backoff
        limiter.backoff_delays[host] = 5.0
        
        # Record success
        limiter.record_success(host, response_time=0.1)
        
        # Backoff should decrease
        assert limiter.backoff_delays[host] < 5.0
    
    def test_get_global_stats(self, limiter):
        """Test global statistics."""
        # Record some requests
        limiter.record_success("host1.com", 0.1)
        limiter.record_success("host2.com", 0.2)
        limiter.record_failure("host3.com", "timeout")
        
        stats = limiter.get_global_stats()
        
        assert stats["total_requests"] == 3
        assert stats["success_rate"] == 2/3
        assert stats["active_hosts"] == 3
        assert stats["avg_response_time"] == 0.15  # (0.1 + 0.2) / 2
    
    def test_get_host_stats_empty(self, limiter):
        """Test host statistics for non-existent host."""
        stats = limiter.get_host_stats("nonexistent.com")
        
        assert stats["total_requests"] == 0
        assert stats["success_rate"] == 0.0
        assert stats["avg_response_time"] == 0.0
        assert stats["current_backoff"] == 0.0


class TestRateLimiter:
    """Test RateLimiter main interface."""
    
    @pytest.fixture
    def rate_limiter(self):
        """Create rate limiter."""
        config = RateLimitConfig(
            requests_per_second=10.0,
            per_host_limit=2.0,
            global_limit=20.0
        )
        return RateLimiter(config)
    
    async def test_context_manager(self, rate_limiter):
        """Test rate limiter as context manager."""
        async with rate_limiter as limiter:
            assert limiter is rate_limiter
    
    async def test_acquire(self, rate_limiter):
        """Test request acquisition."""
        result = await rate_limiter.acquire("test.example.com")
        assert result is True
    
    def test_record_success(self, rate_limiter):
        """Test recording success."""
        rate_limiter.record_success("test.example.com", 0.5)
        
        stats = rate_limiter.get_stats("test.example.com")
        assert stats["total_requests"] == 1
        assert stats["success_rate"] == 1.0
    
    def test_record_failure(self, rate_limiter):
        """Test recording failure."""
        rate_limiter.record_failure("test.example.com", "timeout")
        
        stats = rate_limiter.get_stats("test.example.com")
        assert stats["total_requests"] == 1
        assert stats["success_rate"] == 0.0
    
    def test_get_stats_global(self, rate_limiter):
        """Test getting global statistics."""
        rate_limiter.record_success("host1.com", 0.1)
        rate_limiter.record_failure("host2.com", "error")
        
        stats = rate_limiter.get_stats()  # No host specified = global
        assert stats["total_requests"] == 2
        assert stats["success_rate"] == 0.5
    
    def test_get_stats_host(self, rate_limiter):
        """Test getting host-specific statistics."""
        rate_limiter.record_success("host1.com", 0.1)
        rate_limiter.record_failure("host2.com", "error")
        
        stats1 = rate_limiter.get_stats("host1.com")
        stats2 = rate_limiter.get_stats("host2.com")
        
        assert stats1["success_rate"] == 1.0
        assert stats2["success_rate"] == 0.0
    
    async def test_rate_limiting_integration(self, rate_limiter):
        """Test rate limiting in realistic scenario."""
        host = "test.example.com"
        
        # Make several requests
        for i in range(5):
            try:
                await rate_limiter.acquire(host, timeout=0.1)
                rate_limiter.record_success(host, 0.1)
            except RateLimitExceededError:
                rate_limiter.record_failure(host, "rate_limited")
                break
        
        # Check that some requests succeeded
        stats = rate_limiter.get_stats(host)
        assert stats["total_requests"] > 0
    
    def test_default_config(self):
        """Test rate limiter with default configuration."""
        limiter = RateLimiter()  # No config provided
        
        assert limiter.config.requests_per_second == 10.0
        assert limiter.config.per_host_limit == 2.0

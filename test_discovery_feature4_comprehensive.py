#!/usr/bin/env python3
"""
Comprehensive integration test for Feature 4: Async Device Discovery Service.

This script tests the complete discovery system including:
- Plugin system functionality
- Event system integration
- Scheduling system operation
- Protocol implementations
- Configuration management
- End-to-end discovery workflows
"""

import asyncio
import sys
import time
from pathlib import Path
from datetime import datetime, timezone

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from edge_device_fleet_manager.discovery.core import Device, DeviceStatus
from edge_device_fleet_manager.discovery.config import DiscoveryConfig, ProtocolConfig
from edge_device_fleet_manager.discovery.events import (
    DiscoveryEventBus, DeviceDiscoveredEvent, DiscoveryStartedEvent,
    DiscoveryCompletedEvent, EventFilter
)
from edge_device_fleet_manager.discovery.scheduling import (
    DiscoveryScheduler, ScheduleConfig, DiscoveryJob, JobPriority
)
from edge_device_fleet_manager.discovery.plugins.base import (
    DiscoveryPlugin, PluginConfig, PluginStatus
)
from edge_device_fleet_manager.discovery.plugins.manager import PluginManager
from edge_device_fleet_manager.discovery.plugins.decorators import discovery_plugin


async def test_configuration_system():
    """Test configuration system functionality."""
    print("🔍 Testing Configuration System...")
    
    # Test default configuration
    config = DiscoveryConfig()
    assert config.enabled is True
    assert config.scheduler_enabled is True
    assert config.event_bus_enabled is True
    
    # Test configuration validation
    errors = config.validate()
    assert len(errors) == 0
    
    # Test protocol configuration
    assert config.is_protocol_enabled("mdns") is True
    assert config.is_protocol_enabled("ssdp") is True
    assert config.is_protocol_enabled("snmp") is False  # Disabled by default
    
    # Test protocol settings
    mdns_config = config.get_protocol_config("mdns")
    assert mdns_config is not None
    assert mdns_config.priority == 90
    
    # Test configuration serialization
    config_dict = config.to_dict()
    assert "network" in config_dict
    assert "protocols" in config_dict
    assert "timing" in config_dict
    
    print("✅ Configuration system tests passed")
    return True


async def test_event_system():
    """Test event system functionality."""
    print("🔍 Testing Event System...")
    
    event_bus = DiscoveryEventBus(max_history=10)
    events_received = []
    
    # Test event subscription
    async def event_callback(event):
        events_received.append(event)
    
    sub_id = await event_bus.subscribe(event_callback)
    assert isinstance(sub_id, str)
    
    # Test event publishing
    device = Device(
        ip_address="*************",
        name="Test Device",
        discovery_protocol="test",
        status=DeviceStatus.ONLINE
    )
    
    event = DeviceDiscoveredEvent(
        device=device,
        discovery_protocol="test",
        is_new_device=True
    )
    
    delivered_count = await event_bus.publish(event)
    assert delivered_count == 1
    
    # Wait for async processing
    await asyncio.sleep(0.01)
    
    # Check event was received
    assert len(events_received) == 1
    assert events_received[0].event_type == "device.discovered"
    assert events_received[0].device.name == "Test Device"
    
    # Test event filtering
    filter_events = []
    
    async def filtered_callback(event):
        filter_events.append(event)
    
    event_filter = EventFilter(event_types=["discovery.started"])
    await event_bus.subscribe(filtered_callback, event_filter)
    
    # Publish different event types
    start_event = DiscoveryStartedEvent(protocols=["mdns"])
    await event_bus.publish(start_event)
    
    device_event = DeviceDiscoveredEvent(device=device)
    await event_bus.publish(device_event)
    
    await asyncio.sleep(0.01)
    
    # Filtered callback should only receive start event
    assert len(filter_events) == 1
    assert filter_events[0].event_type == "discovery.started"
    
    # Test event history
    history = await event_bus.get_event_history()
    assert len(history) >= 3
    
    # Test statistics
    stats = await event_bus.get_statistics()
    assert stats["events_published"] >= 3
    assert stats["subscriptions_count"] == 2
    
    await event_bus.shutdown()
    
    print("✅ Event system tests passed")
    return True


async def test_plugin_system():
    """Test plugin system functionality."""
    print("🔍 Testing Plugin System...")
    
    # Create test plugin
    @discovery_plugin(
        name="test_plugin",
        version="1.0.0",
        description="Test plugin for integration testing",
        author="Test Author",
        supported_protocols=["test"]
    )
    class TestPlugin(DiscoveryPlugin):
        async def initialize(self):
            self.initialized = True
        
        async def discover(self, **kwargs):
            from edge_device_fleet_manager.discovery.core import DiscoveryResult
            
            device = Device(
                ip_address="*************",
                name="Plugin Test Device",
                discovery_protocol="test",
                status=DeviceStatus.ONLINE
            )
            
            result = DiscoveryResult(protocol="test", success=True)
            result.add_device(device)
            return result
        
        async def cleanup(self):
            self.cleaned_up = True
    
    # Test plugin creation and lifecycle
    config = PluginConfig(plugin_name="test_plugin")
    plugin = TestPlugin(config)
    
    assert plugin.status == PluginStatus.UNLOADED
    
    # Load plugin
    await plugin.load()
    assert plugin.status == PluginStatus.LOADED
    assert plugin.initialized is True
    
    # Activate plugin
    await plugin.activate()
    assert plugin.status == PluginStatus.ACTIVE
    assert await plugin.is_available() is True
    
    # Test discovery
    result = await plugin.discover()
    assert result.success is True
    assert len(result.devices) == 1
    assert result.devices[0].name == "Plugin Test Device"
    
    # Test plugin statistics
    stats = await plugin.get_statistics()
    assert stats["plugin_name"] == "test_plugin"
    assert stats["status"] == "active"
    
    # Test plugin manager
    plugin_manager = PluginManager(["plugins"], enable_hot_reload=False)
    await plugin_manager.initialize()
    
    # Register plugin class
    metadata = TestPlugin.__plugin_metadata__
    await plugin_manager.registry.register_plugin_class(TestPlugin, metadata)
    
    # Load plugin through manager
    managed_plugin = await plugin_manager.load_plugin("test_plugin")
    assert isinstance(managed_plugin, TestPlugin)
    
    await plugin_manager.stop()
    
    print("✅ Plugin system tests passed")
    return True


async def test_scheduling_system():
    """Test scheduling system functionality."""
    print("🔍 Testing Scheduling System...")
    
    # Create mock discovery engine
    class MockDiscoveryEngine:
        def __init__(self):
            self.discover_calls = []
        
        async def discover_all(self, protocols):
            from edge_device_fleet_manager.discovery.core import DiscoveryResult
            
            self.discover_calls.append(protocols)
            
            device = Device(
                ip_address="*************",
                name="Scheduled Device",
                discovery_protocol="mock",
                status=DeviceStatus.ONLINE
            )
            
            result = DiscoveryResult(protocol="mock", success=True)
            result.add_device(device)
            return result
    
    mock_engine = MockDiscoveryEngine()
    event_bus = DiscoveryEventBus()
    
    # Create scheduler
    schedule_config = ScheduleConfig(
        enabled=True,
        interval_seconds=60,
        max_concurrent_jobs=2,
        job_timeout_seconds=30
    )
    
    scheduler = DiscoveryScheduler(mock_engine, schedule_config, event_bus)
    
    # Start scheduler
    await scheduler.start()
    assert scheduler._running is True
    
    # Schedule a job
    job = DiscoveryJob(
        name="test_job",
        protocols=["mdns", "ssdp"],
        priority=JobPriority.HIGH,
        scheduled_time=datetime.now(timezone.utc)
    )
    
    job_id = await scheduler.schedule_job(job)
    assert isinstance(job_id, str)
    
    # Wait for job execution
    await asyncio.sleep(0.2)
    
    # Check job was executed
    assert len(mock_engine.discover_calls) > 0
    assert mock_engine.discover_calls[0] == ["mdns", "ssdp"]
    
    # Check job status
    executed_job = await scheduler.get_job(job_id)
    assert executed_job.status.value in ["completed", "running"]
    
    # Test scheduler statistics
    stats = await scheduler.get_statistics()
    assert stats["running"] is True
    assert stats["jobs_scheduled"] >= 1
    
    # Stop scheduler
    await scheduler.stop()
    assert scheduler._running is False
    
    await event_bus.shutdown()
    
    print("✅ Scheduling system tests passed")
    return True


async def test_protocol_availability():
    """Test protocol availability."""
    print("🔍 Testing Protocol Availability...")
    
    # Test protocol imports and basic functionality
    try:
        from edge_device_fleet_manager.discovery.protocols.mdns import MDNSDiscovery
        mdns_discovery = MDNSDiscovery()
        mdns_available = await mdns_discovery.is_available()
        print(f"  📡 mDNS available: {mdns_available}")
    except Exception as e:
        print(f"  ⚠️  mDNS test skipped: {e}")
    
    try:
        from edge_device_fleet_manager.discovery.protocols.ssdp import SSDPDiscovery
        ssdp_discovery = SSDPDiscovery()
        ssdp_available = await ssdp_discovery.is_available()
        print(f"  📡 SSDP available: {ssdp_available}")
    except Exception as e:
        print(f"  ⚠️  SSDP test skipped: {e}")
    
    try:
        from edge_device_fleet_manager.discovery.protocols.snmp import SNMPDiscovery
        snmp_discovery = SNMPDiscovery()
        snmp_available = await snmp_discovery.is_available()
        print(f"  📡 SNMP available: {snmp_available}")
    except Exception as e:
        print(f"  ⚠️  SNMP test skipped: {e}")
    
    try:
        from edge_device_fleet_manager.discovery.protocols.network_scan import NetworkScanDiscovery
        network_discovery = NetworkScanDiscovery()
        network_available = await network_discovery.is_available()
        print(f"  📡 Network Scan available: {network_available}")
    except Exception as e:
        print(f"  ⚠️  Network Scan test skipped: {e}")
    
    print("✅ Protocol availability tests passed")
    return True


async def test_end_to_end_workflow():
    """Test end-to-end discovery workflow."""
    print("🔍 Testing End-to-End Workflow...")
    
    # Create components
    config = DiscoveryConfig()
    event_bus = DiscoveryEventBus()
    
    events_received = []
    
    async def workflow_callback(event):
        events_received.append(event)
    
    await event_bus.subscribe(workflow_callback)
    
    # Create mock discovery engine
    class WorkflowMockEngine:
        async def discover_all(self, protocols):
            from edge_device_fleet_manager.discovery.core import DiscoveryResult
            
            # Simulate discovery
            await asyncio.sleep(0.01)
            
            device = Device(
                ip_address="*************",
                name="Workflow Test Device",
                discovery_protocol="workflow",
                status=DeviceStatus.ONLINE
            )
            
            result = DiscoveryResult(protocol="workflow", success=True)
            result.add_device(device)
            return result
    
    mock_engine = WorkflowMockEngine()
    
    # Create scheduler with mock engine
    schedule_config = ScheduleConfig(
        enabled=True,
        max_concurrent_jobs=1,
        job_timeout_seconds=10
    )
    
    scheduler = DiscoveryScheduler(mock_engine, schedule_config, event_bus)
    await scheduler.start()
    
    # Schedule discovery job
    job = DiscoveryJob(
        name="workflow_test",
        protocols=["workflow"],
        scheduled_time=datetime.now(timezone.utc)
    )
    
    await scheduler.schedule_job(job)
    
    # Wait for workflow completion
    await asyncio.sleep(0.1)
    
    # Check events were generated
    assert len(events_received) >= 2
    
    event_types = [event.event_type for event in events_received]
    assert "discovery.started" in event_types
    assert "discovery.completed" in event_types
    
    # Cleanup
    await scheduler.stop()
    await event_bus.shutdown()
    
    print("✅ End-to-end workflow tests passed")
    return True


async def run_all_tests():
    """Run all discovery system tests."""
    print("🚀 Starting Feature 4: Async Device Discovery Service Tests")
    print("=" * 70)
    
    tests = [
        test_configuration_system,
        test_event_system,
        test_plugin_system,
        test_scheduling_system,
        test_protocol_availability,
        test_end_to_end_workflow,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            start_time = time.time()
            result = await test()
            duration = time.time() - start_time
            
            if result:
                passed += 1
                print(f"  ⏱️  Completed in {duration:.3f}s")
            else:
                failed += 1
                print(f"  ❌ Test failed")
        
        except Exception as e:
            failed += 1
            print(f"  ❌ Test failed with exception: {e}")
            import traceback
            traceback.print_exc()
        
        print()
    
    print("=" * 70)
    print(f"🎯 Summary: {passed}/{len(tests)} tests passed")
    
    if failed == 0:
        print("🎉 All discovery tests passed! Feature 4 is working correctly.")
        return True
    else:
        print(f"❌ {failed} tests failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = asyncio.run(run_all_tests())
    sys.exit(0 if success else 1)

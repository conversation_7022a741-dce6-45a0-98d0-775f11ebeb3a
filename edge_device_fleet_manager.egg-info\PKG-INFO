Metadata-Version: 2.4
Name: edge-device-fleet-manager
Version: 0.1.0
Summary: Production-grade Python CLI and library for IoT edge device management at scale
Author-email: Edge Device Fleet Manager Team <<EMAIL>>
License: MIT
Project-URL: Homepage, https://github.com/edge-fleet/edge-device-fleet-manager
Project-URL: Documentation, https://edge-fleet.github.io/edge-device-fleet-manager
Project-URL: Repository, https://github.com/edge-fleet/edge-device-fleet-manager
Project-URL: Issues, https://github.com/edge-fleet/edge-device-fleet-manager/issues
Project-URL: Changelog, https://github.com/edge-fleet/edge-device-fleet-manager/blob/main/CHANGELOG.md
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: System Administrators
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Topic :: System :: Monitoring
Classifier: Topic :: System :: Networking :: Monitoring
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.8
Description-Content-Type: text/markdown
Requires-Dist: click>=8.1.0
Requires-Dist: rich>=13.0.0
Requires-Dist: typer>=0.9.0
Requires-Dist: pydantic>=2.0.0
Requires-Dist: pydantic-settings>=2.0.0
Requires-Dist: python-dotenv>=1.0.0
Requires-Dist: pyyaml>=6.0
Requires-Dist: cryptography>=41.0.0
Requires-Dist: boto3>=1.28.0
Requires-Dist: botocore>=1.31.0
Requires-Dist: watchdog>=3.0.0
Requires-Dist: importlib-metadata>=6.0.0
Requires-Dist: asyncio-mqtt>=0.16.0
Requires-Dist: aiofiles>=23.0.0
Requires-Dist: anyio>=4.0.0
Requires-Dist: structlog>=23.0.0
Requires-Dist: sentry-sdk>=1.32.0
Requires-Dist: prometheus-client>=0.17.0
Requires-Dist: sqlalchemy>=2.0.0
Requires-Dist: alembic>=1.12.0
Requires-Dist: asyncpg>=0.28.0
Requires-Dist: psycopg2-binary>=2.9.0
Requires-Dist: redis>=5.0.0
Requires-Dist: hiredis>=2.2.0
Requires-Dist: pandas>=2.0.0
Requires-Dist: numpy>=1.24.0
Requires-Dist: reactivex>=4.0.0
Requires-Dist: jinja2>=3.1.0
Requires-Dist: httpx>=0.25.0
Requires-Dist: tenacity>=8.2.0
Requires-Dist: jsonschema>=4.19.0
Provides-Extra: dev
Requires-Dist: pytest>=7.4.0; extra == "dev"
Requires-Dist: pytest-asyncio>=0.21.0; extra == "dev"
Requires-Dist: pytest-cov>=4.1.0; extra == "dev"
Requires-Dist: pytest-mock>=3.11.0; extra == "dev"
Requires-Dist: pytest-xdist>=3.3.0; extra == "dev"
Requires-Dist: hypothesis>=6.82.0; extra == "dev"
Requires-Dist: factory-boy>=3.3.0; extra == "dev"
Requires-Dist: black>=23.7.0; extra == "dev"
Requires-Dist: isort>=5.12.0; extra == "dev"
Requires-Dist: flake8>=6.0.0; extra == "dev"
Requires-Dist: mypy>=1.5.0; extra == "dev"
Requires-Dist: pylint>=2.17.0; extra == "dev"
Requires-Dist: bandit>=1.7.0; extra == "dev"
Requires-Dist: safety>=2.3.0; extra == "dev"
Requires-Dist: detect-secrets>=1.4.0; extra == "dev"
Requires-Dist: pre-commit>=3.3.0; extra == "dev"
Requires-Dist: mkdocs>=1.5.0; extra == "dev"
Requires-Dist: mkdocs-material>=9.2.0; extra == "dev"
Requires-Dist: mkdocs-mermaid2-plugin>=1.1.0; extra == "dev"
Requires-Dist: ipython>=8.14.0; extra == "dev"
Requires-Dist: ipdb>=0.13.0; extra == "dev"
Requires-Dist: memory-profiler>=0.61.0; extra == "dev"
Requires-Dist: line-profiler>=4.1.0; extra == "dev"
Requires-Dist: build>=0.10.0; extra == "dev"
Requires-Dist: twine>=4.0.0; extra == "dev"
Requires-Dist: setuptools-scm>=8.0.0; extra == "dev"
Requires-Dist: docker>=6.1.0; extra == "dev"
Requires-Dist: tox>=4.11.0; extra == "dev"
Requires-Dist: tox-parallel>=0.6.0; extra == "dev"
Provides-Extra: docs
Requires-Dist: mkdocs>=1.5.0; extra == "docs"
Requires-Dist: mkdocs-material>=9.2.0; extra == "docs"
Requires-Dist: mkdocs-mermaid2-plugin>=1.1.0; extra == "docs"

# Edge Device Fleet Manager

[![CI/CD Pipeline](https://github.com/edge-fleet/edge-device-fleet-manager/workflows/CI/CD%20Pipeline/badge.svg)](https://github.com/edge-fleet/edge-device-fleet-manager/actions)
[![codecov](https://codecov.io/gh/edge-fleet/edge-device-fleet-manager/branch/main/graph/badge.svg)](https://codecov.io/gh/edge-fleet/edge-device-fleet-manager)
[![PyPI version](https://badge.fury.io/py/edge-device-fleet-manager.svg)](https://badge.fury.io/py/edge-device-fleet-manager)
[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

A production-grade Python CLI and library to discover, configure, monitor, and maintain IoT edge devices at scale. Features a hot-reloadable plugin-driven CLI, multi-tier configuration with encrypted secrets, and async device discovery over mDNS/SSDP.

## 🚀 Features

### Feature 1: Meta-Driven CLI & Configuration ✅

- **Hot-Reloadable Plugin System**: Watchdog-powered plugin loader that updates Click's registry on file events without restart
- **Three-Tier Configuration**: YAML defaults, .env overrides, and encrypted AWS Secrets Manager entries with auto-rotation
- **ContextVar-Based Context Management**: Propagates shared objects across sync and async Click commands
- **Structured JSON Logging**: 5% DEBUG sampling, correlation IDs, and asynchronous Sentry integration
- **Quality Enforcement**: Git hooks with pre-commit, black, isort, flake8, mypy --strict, and detect-secrets
- **Custom Click Types**: DeviceIDType with remote schema validation, caching, and shell autocompletion
- **CI/CD Pipeline**: GitHub Actions with Python 3.8-3.11 matrix, multi-stage Docker builds
- **Debug REPL**: Hidden command launching IPython preloaded with app context

## 📦 Installation

### From PyPI (Recommended)

```bash
pip install edge-device-fleet-manager
```

### From Source

```bash
git clone https://github.com/edge-fleet/edge-device-fleet-manager.git
cd edge-device-fleet-manager
pip install -e ".[dev]"
```

### Using Docker

```bash
docker pull edgefleet/edge-device-fleet-manager:latest
docker run --rm edgefleet/edge-device-fleet-manager:latest --help
```

## 🏃‍♂️ Quick Start

### Basic Usage

```bash
# Show help
edge-fleet --help

# Show current configuration
edge-fleet config

# List loaded plugins
edge-fleet plugins

# Reload a specific plugin
edge-fleet reload-plugin --name sample

# Access debug REPL (hidden command)
edge-fleet debug-repl
```

### Configuration

The system uses a three-tier configuration approach:

1. **YAML Defaults** (`configs/default.yaml`)
2. **Environment Variables** (`.env` file)
3. **AWS Secrets Manager** (encrypted secrets with auto-rotation)

## 🛠️ Development Mode Setup

### Prerequisites

- Python 3.8+
- AWS CLI configured (for Secrets Manager)
- Docker (optional)
- Redis (for caching)

### Local Development Setup

1. **Clone and Install**:
   ```bash
   git clone https://github.com/edge-fleet/edge-device-fleet-manager.git
   cd edge-device-fleet-manager
   pip install -e ".[dev]"
   ```

2. **Set up Environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Install Pre-commit Hooks**:
   ```bash
   pre-commit install
   pre-commit install --hook-type pre-push
   ```

4. **Run Tests**:
   ```bash
   pytest
   ```

### AWS Secrets Manager Setup

#### Local Development with LocalStack

For development, you can use LocalStack to emulate AWS Secrets Manager:

```bash
# Install LocalStack
pip install localstack

# Start LocalStack
localstack start -d

# Configure AWS CLI for LocalStack
aws configure set aws_access_key_id test
aws configure set aws_secret_access_key test
aws configure set region us-east-1
aws configure set endpoint_url http://localhost:4566

# Create test secrets
aws secretsmanager create-secret \
    --name edge-fleet-manager/secrets \
    --secret-string '{"database__password":"test_db_pass","mqtt__password":"test_mqtt_pass"}' \
    --endpoint-url http://localhost:4566

aws secretsmanager create-secret \
    --name edge-fleet-manager/encryption-key \
    --secret-string '{"key":"your-base64-encoded-fernet-key"}' \
    --endpoint-url http://localhost:4566
```

#### Production AWS Setup

1. **Create IAM Role/User** with permissions:
   ```json
   {
       "Version": "2012-10-17",
       "Statement": [
           {
               "Effect": "Allow",
               "Action": [
                   "secretsmanager:GetSecretValue",
                   "secretsmanager:CreateSecret",
                   "secretsmanager:UpdateSecret",
                   "secretsmanager:DescribeSecret"
               ],
               "Resource": [
                   "arn:aws:secretsmanager:*:*:secret:edge-fleet-manager/*"
               ]
           }
       ]
   }
   ```

2. **Create Secrets**:
   ```bash
   # Create encryption key
   aws secretsmanager create-secret \
       --name edge-fleet-manager/encryption-key \
       --secret-string '{"key":"'$(python -c "from cryptography.fernet import Fernet; print(Fernet.generate_key().decode())")'"}'

   # Create application secrets
   aws secretsmanager create-secret \
       --name edge-fleet-manager/secrets \
       --secret-string '{
           "database__password":"your_secure_db_password",
           "mqtt__password":"your_secure_mqtt_password",
           "redis__password":"your_secure_redis_password",
           "sentry_dsn":"your_sentry_dsn"
       }'
   ```

### Plugin Development

Create custom plugins in the `plugins/` directory:

```python
# plugins/my_plugin.py
import click
from edge_device_fleet_manager.core.plugins import Plugin, PluginMetadata
from edge_device_fleet_manager.core.context import app_context, get_logger

logger = get_logger(__name__)

class MyPlugin(Plugin):
    metadata = PluginMetadata(
        name="my_plugin",
        version="1.0.0",
        description="My custom plugin",
        author="Your Name",
        commands=["my-command"]
    )
    
    def initialize(self, config):
        logger.info("My plugin initialized", config_debug=config.debug)
    
    @click.command()
    @click.option('--message', default='Hello', help='Message to display')
    def my_command(self, message: str):
        """My custom command."""
        correlation_id = app_context.correlation_id
        logger.info("My command executed", message=message, correlation_id=correlation_id)
        click.echo(f"{message} from my plugin! (ID: {correlation_id})")
```

The plugin will be automatically loaded and available as:
```bash
edge-fleet my-command --message "Custom message"
```

## 🧪 Testing

### Running Tests

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=edge_device_fleet_manager --cov-report=html

# Run specific test categories
pytest -m unit          # Unit tests only
pytest -m integration   # Integration tests only
pytest -m "not slow"    # Skip slow tests

# Run tests in parallel
pytest -n auto
```

### Test Categories

- **Unit Tests**: Fast, isolated tests for individual components
- **Integration Tests**: Tests that verify component interactions
- **Slow Tests**: Long-running tests (marked with `@pytest.mark.slow`)

### Feature 1 Specific Tests

The plugin system includes comprehensive tests that specifically address the requirement:

> "Using pytest-asyncio and Click's CliRunner, simulate a plugin load error (syntax exception) and assert the CLI logs a warning yet continues loading others."

Run the specific test:
```bash
pytest tests/unit/test_plugins.py::TestPluginSystem::test_plugin_load_error_continues_loading_others -v
```

## 🔧 Configuration Reference

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `ENVIRONMENT` | Environment name | `development` |
| `DEBUG` | Enable debug mode | `false` |
| `DATABASE__URL` | Database connection URL | `sqlite:///edge_fleet.db` |
| `MQTT__BROKER_HOST` | MQTT broker hostname | `localhost` |
| `MQTT__BROKER_PORT` | MQTT broker port | `1883` |
| `REDIS__HOST` | Redis hostname | `localhost` |
| `REDIS__PORT` | Redis port | `6379` |
| `LOGGING__LEVEL` | Logging level | `INFO` |
| `PLUGINS__AUTO_RELOAD` | Enable plugin hot-reload | `true` |

### Configuration Files

- `configs/default.yaml`: Default configuration
- `configs/development.yaml`: Development overrides
- `configs/production.yaml`: Production overrides
- `.env`: Local environment variables

## 🐳 Docker Usage

### Development

```bash
# Build development image
docker build -t edge-fleet-dev .

# Run with volume mounts for development
docker run -it --rm \
    -v $(pwd)/plugins:/app/plugins \
    -v $(pwd)/configs:/app/configs \
    -e DEBUG=true \
    edge-fleet-dev
```

### Production

```bash
# Pull and run production image
docker run -d \
    --name edge-fleet-manager \
    -e ENVIRONMENT=production \
    -e DATABASE__URL=******************************/edge_fleet \
    edgefleet/edge-device-fleet-manager:latest
```

## 📊 Monitoring and Observability

### Logging

- **Structured JSON logs** with correlation IDs
- **Debug sampling** (5% by default) to reduce log volume
- **Sentry integration** for error tracking and performance monitoring

### Metrics

- **Prometheus metrics** exposed on `/metrics` endpoint
- **Plugin load times** and **error rates**
- **Command execution metrics**

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes and add tests
4. Run the test suite: `pytest`
5. Run quality checks: `pre-commit run --all-files`
6. Commit your changes: `git commit -m 'Add amazing feature'`
7. Push to the branch: `git push origin feature/amazing-feature`
8. Open a Pull Request

### Code Quality

This project enforces strict code quality standards:

- **Black** for code formatting
- **isort** for import sorting
- **flake8** for linting
- **mypy** for type checking (strict mode)
- **bandit** for security scanning
- **detect-secrets** for secret detection

All checks run automatically via pre-commit hooks and CI/CD.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Built with [Click](https://click.palletsprojects.com/) for the CLI framework
- [Pydantic](https://pydantic-docs.helpmanual.io/) for configuration management
- [Watchdog](https://python-watchdog.readthedocs.io/) for file system monitoring
- [Rich](https://rich.readthedocs.io/) for beautiful terminal output

## 📞 Support

- **Documentation**: [https://edge-fleet.github.io/edge-device-fleet-manager](https://edge-fleet.github.io/edge-device-fleet-manager)
- **Issues**: [GitHub Issues](https://github.com/edge-fleet/edge-device-fleet-manager/issues)
- **Discussions**: [GitHub Discussions](https://github.com/edge-fleet/edge-device-fleet-manager/discussions)

## 🏃‍♂️ Running Feature 1 Tests

### Quick Test Run

```bash
# Run all Feature 1 tests
python scripts/run_tests.py feature1 -v -c

# Run specific plugin error handling test (addresses the prompt requirement)
pytest tests/unit/test_plugins.py::TestPluginSystem::test_plugin_load_error_continues_loading_others -v

# Run all plugin tests
python scripts/run_tests.py plugins -v

# Run CLI tests
python scripts/run_tests.py cli -v

# Run configuration tests
python scripts/run_tests.py config -v

# Run context management tests
python scripts/run_tests.py context -v
```

### Using Make Commands

```bash
# Install development dependencies
make install-dev

# Run all tests with coverage
make test-coverage

# Run unit tests only
make test-unit

# Run linting and type checking
make lint
make type-check

# Format code
make format

# Run security checks
make security-check

# Run all pre-commit hooks
make pre-commit
```

### Using Tox (Multi-Python Testing)

```bash
# Test across all Python versions
tox

# Test specific Python version
tox -e py311

# Run linting
tox -e lint

# Run type checking
tox -e type-check

# Run security checks
tox -e security

# Run with coverage
tox -e coverage
```

### Docker Testing

```bash
# Build and test in Docker
make docker-build
docker run --rm edge-device-fleet-manager:latest --help

# Run full development stack
docker-compose up -d
docker-compose exec edge-fleet-manager edge-fleet --help
```

## 📋 Feature 1 Implementation Checklist

- ✅ **Watchdog-powered plugin loader** with hot-reload capability
- ✅ **Three-tier configuration** (YAML → .env → AWS Secrets Manager)
- ✅ **ContextVar-based context management** for shared objects
- ✅ **Structured JSON logging** with DEBUG sampling and Sentry integration
- ✅ **Quality enforcement** via Git hooks and pre-commit
- ✅ **Custom Click types** with validation and autocompletion
- ✅ **GitHub Actions CI/CD** with Python matrix testing
- ✅ **Debug REPL command** with IPython and app context
- ✅ **Comprehensive unit tests** including plugin error handling
- ✅ **Multi-stage Docker builds** with security scanning

### Key Test Coverage

The implementation specifically addresses the prompt requirement:

> "Using pytest-asyncio and Click's CliRunner, simulate a plugin load error (syntax exception) and assert the CLI logs a warning yet continues loading others."

This is implemented in `tests/unit/test_plugins.py::TestPluginSystem::test_plugin_load_error_continues_loading_others`

## 🎯 Next Steps for Additional Features

This completes **Feature 1: Meta-Driven CLI & Configuration**. The foundation is now ready for implementing the remaining features:

- **Feature 2**: High-Performance Device Discovery
- **Feature 3**: Domain-Driven Device Repository
- **Feature 4**: Telemetry Ingestion & Advanced Analytics
- **Feature 5**: Robust Persistence & Migrations
- **Feature 6**: Dynamic Visualization & Dashboard
- **Feature 7**: Enterprise-Grade Export & Alerting
- **Feature 8**: CI/CD, Packaging & Observability

---

**Edge Device Fleet Manager** - Bringing production-grade IoT device management to scale! 🚀

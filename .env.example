# Example environment variables for Edge Device Fleet Manager
# Copy this file to .env and update with your actual values

# Application settings
ENVIRONMENT=development
DEBUG=true

# Database settings
DATABASE__URL=sqlite:///edge_fleet.db
DATABASE__ECHO=false

# MQTT settings
MQTT__BROKER_HOST=localhost
MQTT__BROKER_PORT=1883
MQTT__USERNAME=
MQTT__PASSWORD=
MQTT__CLIENT_ID=edge_fleet_manager

# Redis settings
REDIS__HOST=localhost
REDIS__PORT=6379
REDIS__DB=0
REDIS__PASSWORD=

# Logging settings
LOGGING__LEVEL=DEBUG
LOGGING__SENTRY_DSN=
LOGGING__SENTRY_ENVIRONMENT=development

# AWS settings
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_SESSION_TOKEN=
AWS_DEFAULT_REGION=us-east-1

# Secrets Manager settings
SECRETS__REGION_NAME=us-east-1
SECRETS__SECRET_NAME=edge-fleet-manager/secrets
SECRETS__ENCRYPTION_KEY_NAME=edge-fleet-manager/encryption-key
SECRETS__KMS_KEY_ID=

# Plugin settings
PLUGINS__PLUGINS_DIR=plugins
PLUGINS__AUTO_RELOAD=true
PLUGINS__RELOAD_DELAY=1.0

# Discovery settings
DISCOVERY__MDNS_TIMEOUT=5
DISCOVERY__SSDP_TIMEOUT=10
DISCOVERY__MAX_RETRIES=10
DISCOVERY__RATE_LIMIT_PER_HOST=10
DISCOVERY__RATE_LIMIT_GLOBAL=100

[build-system]
requires = ["setuptools>=61.0", "setuptools-scm>=8.0"]
build-backend = "setuptools.build_meta"

[project]
name = "edge-device-fleet-manager"
description = "Production-grade Python CLI and library for IoT edge device management at scale"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "Edge Device Fleet Manager Team", email = "<EMAIL>"}
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Intended Audience :: System Administrators",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: System :: Monitoring",
    "Topic :: System :: Networking :: Monitoring",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
requires-python = ">=3.8"
dynamic = ["version"]

dependencies = [
    # CLI Framework
    "click>=8.1.0",
    "rich>=13.0.0",
    "typer>=0.9.0",
    
    # Configuration Management
    "pydantic>=2.0.0",
    "pydantic-settings>=2.0.0",
    "python-dotenv>=1.0.0",
    "pyyaml>=6.0",
    "cryptography>=41.0.0",
    
    # AWS Integration
    "boto3>=1.28.0",
    "botocore>=1.31.0",
    
    # Plugin System & Hot Reload
    "watchdog>=3.0.0",
    "importlib-metadata>=6.0.0",
    
    # Async Support
    "asyncio-mqtt>=0.16.0",
    "aiofiles>=23.0.0",
    "anyio>=4.0.0",
    
    # Logging & Monitoring
    "structlog>=23.0.0",
    "sentry-sdk>=1.32.0",
    "prometheus-client>=0.17.0",
    
    # Context Management
    "contextvars>=2.4",
    
    # Database
    "sqlalchemy>=2.0.0",
    "alembic>=1.12.0",
    "asyncpg>=0.28.0",
    "psycopg2-binary>=2.9.0",
    
    # Caching
    "redis>=5.0.0",
    "hiredis>=2.2.0",
    
    # Network Discovery
    "zeroconf>=0.112.0",
    "upnpclient>=0.0.8",
    
    # Data Processing
    "pandas>=2.0.0",
    "numpy>=1.24.0",
    "rxpy>=4.0.0",
    
    # Visualization
    "matplotlib>=3.7.0",
    "bokeh>=3.2.0",
    "plotly>=5.15.0",
    
    # Web Framework
    "fastapi>=0.103.0",
    "uvicorn>=0.23.0",
    "websockets>=11.0.0",
    
    # Reporting
    "jinja2>=3.1.0",
    "weasyprint>=60.0",
    "pypdf2>=3.0.0",
    
    # Communication
    "twilio>=8.8.0",
    "sendgrid>=6.10.0",
    
    # Observability
    "opentelemetry-api>=1.20.0",
    "opentelemetry-sdk>=1.20.0",
    "opentelemetry-exporter-jaeger>=1.20.0",
    "opentelemetry-instrumentation>=0.41b0",
    
    # Utilities
    "httpx>=0.25.0",
    "tenacity>=8.2.0",
    "jsonschema>=4.19.0",
]

[project.optional-dependencies]
dev = [
    # Testing
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.11.0",
    "pytest-xdist>=3.3.0",
    "hypothesis>=6.82.0",
    "factory-boy>=3.3.0",
    
    # Code Quality
    "black>=23.7.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
    "pylint>=2.17.0",
    "bandit>=1.7.0",
    "safety>=2.3.0",
    "detect-secrets>=1.4.0",
    
    # Pre-commit
    "pre-commit>=3.3.0",
    
    # Documentation
    "mkdocs>=1.5.0",
    "mkdocs-material>=9.2.0",
    "mkdocs-mermaid2-plugin>=1.1.0",
    
    # Development Tools
    "ipython>=8.14.0",
    "ipdb>=0.13.0",
    "memory-profiler>=0.61.0",
    "line-profiler>=4.1.0",
    
    # Build Tools
    "build>=0.10.0",
    "twine>=4.0.0",
    "setuptools-scm>=8.0.0",
    
    # Container Tools
    "docker>=6.1.0",
    
    # Parallel Testing
    "tox>=4.11.0",
    "tox-parallel>=0.6.0",
]

docs = [
    "mkdocs>=1.5.0",
    "mkdocs-material>=9.2.0",
    "mkdocs-mermaid2-plugin>=1.1.0",
]

[project.urls]
Homepage = "https://github.com/edge-fleet/edge-device-fleet-manager"
Documentation = "https://edge-fleet.github.io/edge-device-fleet-manager"
Repository = "https://github.com/edge-fleet/edge-device-fleet-manager"
Issues = "https://github.com/edge-fleet/edge-device-fleet-manager/issues"
Changelog = "https://github.com/edge-fleet/edge-device-fleet-manager/blob/main/CHANGELOG.md"

[project.scripts]
edge-fleet = "edge_device_fleet_manager.cli.main:main"
edfm = "edge_device_fleet_manager.cli.main:main"

[project.entry-points."edge_device_fleet_manager.plugins"]
# Plugin entry points will be auto-discovered

[tool.setuptools]
packages = ["edge_device_fleet_manager"]

[tool.setuptools.dynamic]
version = {attr = "edge_device_fleet_manager.__version__"}

[tool.setuptools_scm]
write_to = "edge_device_fleet_manager/_version.py"

# Black configuration
[tool.black]
line-length = 88
target-version = ['py38', 'py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# isort configuration
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["edge_device_fleet_manager"]
known_third_party = ["click", "pydantic", "sqlalchemy", "fastapi"]

# mypy configuration
[tool.mypy]
python_version = "3.8"
strict = true
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "watchdog.*",
    "boto3.*",
    "botocore.*",
    "zeroconf.*",
    "upnpclient.*",
    "rxpy.*",
    "twilio.*",
    "sendgrid.*",
    "weasyprint.*",
    "pypdf2.*",
]
ignore_missing_imports = true

# pytest configuration
[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=edge_device_fleet_manager",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
    "--cov-fail-under=90",
]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "asyncio: marks tests as async tests",
]
asyncio_mode = "auto"

# Coverage configuration
[tool.coverage.run]
source = ["edge_device_fleet_manager"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/_version.py",
    "*/migrations/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

# Bandit security linter
[tool.bandit]
exclude_dirs = ["tests", "build", "dist"]
skips = ["B101", "B601"]

# pylint configuration
[tool.pylint.messages_control]
disable = [
    "C0330",  # Wrong hanging indentation
    "C0326",  # Bad whitespace
    "R0903",  # Too few public methods
    "R0913",  # Too many arguments
    "W0613",  # Unused argument
]

[tool.pylint.format]
max-line-length = 88

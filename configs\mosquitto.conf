# Mosquitto MQTT Broker Configuration for Edge Device Fleet Manager

# General settings
pid_file /var/run/mosquitto.pid
persistence true
persistence_location /mosquitto/data/
log_dest file /mosquitto/log/mosquitto.log
log_type error
log_type warning
log_type notice
log_type information

# Network settings
listener 1883
protocol mqtt

# WebSocket listener
listener 9001
protocol websockets

# Security settings
allow_anonymous true
# password_file /mosquitto/config/passwd
# acl_file /mosquitto/config/acl

# Connection settings
max_connections 1000
max_inflight_messages 100
max_queued_messages 1000

# Message settings
message_size_limit 268435456
retain_available true
set_tcp_nodelay true

# Logging
connection_messages true
log_timestamp true

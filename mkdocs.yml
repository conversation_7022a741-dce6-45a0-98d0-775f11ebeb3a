# MkDocs configuration for Edge Device Fleet Manager

site_name: Edge Device Fleet Manager
site_description: Production-grade Python CLI and library for IoT edge device management at scale
site_author: Edge Fleet Team
site_url: https://edge-fleet.github.io/edge-device-fleet-manager

# Repository
repo_name: edge-fleet/edge-device-fleet-manager
repo_url: https://github.com/edge-fleet/edge-device-fleet-manager
edit_uri: edit/main/docs/

# Copyright
copyright: Copyright &copy; 2024 Edge Fleet Team

# Configuration
theme:
  name: material
  language: en
  palette:
    - scheme: default
      primary: blue
      accent: blue
      toggle:
        icon: material/brightness-7
        name: Switch to dark mode
    - scheme: slate
      primary: blue
      accent: blue
      toggle:
        icon: material/brightness-4
        name: Switch to light mode
  font:
    text: Roboto
    code: Roboto Mono
  features:
    - navigation.tabs
    - navigation.sections
    - navigation.expand
    - navigation.top
    - search.highlight
    - search.share
    - content.code.annotate
    - content.code.copy

# Plugins
plugins:
  - search
  - mermaid2:
      arguments:
        theme: base
        themeVariables:
          primaryColor: '#1976d2'
          primaryTextColor: '#ffffff'

# Extensions
markdown_extensions:
  - admonition
  - pymdownx.details
  - pymdownx.superfences:
      custom_fences:
        - name: mermaid
          class: mermaid
          format: !!python/name:mermaid2.fence_mermaid
  - pymdownx.highlight:
      anchor_linenums: true
  - pymdownx.inlinehilite
  - pymdownx.snippets
  - pymdownx.tabbed:
      alternate_style: true
  - pymdownx.tasklist:
      custom_checkbox: true
  - attr_list
  - md_in_html
  - toc:
      permalink: true

# Navigation
nav:
  - Home: index.md
  - Getting Started:
    - Installation: getting-started/installation.md
    - Quick Start: getting-started/quick-start.md
    - Configuration: getting-started/configuration.md
  - Features:
    - CLI & Configuration: features/cli-configuration.md
    - Plugin System: features/plugin-system.md
    - Device Discovery: features/device-discovery.md
    - Repository Pattern: features/repository.md
    - Telemetry & Analytics: features/telemetry.md
    - Persistence: features/persistence.md
    - Visualization: features/visualization.md
    - Export & Alerting: features/export-alerting.md
  - Development:
    - Development Setup: development/setup.md
    - Plugin Development: development/plugins.md
    - Testing: development/testing.md
    - Contributing: development/contributing.md
  - Deployment:
    - Docker: deployment/docker.md
    - AWS Setup: deployment/aws.md
    - Production: deployment/production.md
  - API Reference:
    - Core: api/core.md
    - CLI: api/cli.md
    - Plugins: api/plugins.md
    - Utils: api/utils.md
  - Changelog: changelog.md

# Extra
extra:
  social:
    - icon: fontawesome/brands/github
      link: https://github.com/edge-fleet/edge-device-fleet-manager
    - icon: fontawesome/brands/python
      link: https://pypi.org/project/edge-device-fleet-manager/
  version:
    provider: mike

# Extra CSS
extra_css:
  - stylesheets/extra.css

<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">34%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-07-11 19:01 +0530
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_73bda9688a204f7a___init___py.html">edge_device_fleet_manager\__init__.py</a></td>
                <td class="name left"><a href="z_73bda9688a204f7a___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c___init___py.html">edge_device_fleet_manager\cli\__init__.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html#t26">edge_device_fleet_manager\cli\main.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html#t26"><data value='AsyncGroup'>AsyncGroup</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html">edge_device_fleet_manager\cli\main.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>160</td>
                <td>122</td>
                <td>2</td>
                <td class="right" data-ratio="38 160">24%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t22">edge_device_fleet_manager\cli\types.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t22"><data value='CachedSchema'>CachedSchema</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t39">edge_device_fleet_manager\cli\types.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t39"><data value='DeviceIDType'>DeviceIDType</data></a></td>
                <td>79</td>
                <td>79</td>
                <td>0</td>
                <td class="right" data-ratio="0 79">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t248">edge_device_fleet_manager\cli\types.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t248"><data value='IPAddressType'>IPAddressType</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t289">edge_device_fleet_manager\cli\types.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t289"><data value='SubnetType'>SubnetType</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html">edge_device_fleet_manager\cli\types.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t23">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t23"><data value='DatabaseConfig'>DatabaseConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t33">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t33"><data value='MQTTConfig'>MQTTConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t45">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t45"><data value='RedisConfig'>RedisConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t56">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t56"><data value='LoggingConfig'>LoggingConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t67">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t67"><data value='SecretsConfig'>SecretsConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t76">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t76"><data value='PluginConfig'>PluginConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t85">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t85"><data value='DiscoveryConfig'>DiscoveryConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t97">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t97"><data value='Config'>Config</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t123">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t123"><data value='Config'>Config.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t150">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t150"><data value='SecretsManager'>SecretsManager</data></a></td>
                <td>97</td>
                <td>97</td>
                <td>0</td>
                <td class="right" data-ratio="0 97">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t398">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t398"><data value='ConfigLoader'>ConfigLoader</data></a></td>
                <td>39</td>
                <td>39</td>
                <td>0</td>
                <td class="right" data-ratio="0 39">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>120</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="112 120">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t30">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t30"><data value='AppContext'>AppContext</data></a></td>
                <td>26</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="9 26">35%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>143</td>
                <td>82</td>
                <td>0</td>
                <td class="right" data-ratio="61 143">43%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t8">edge_device_fleet_manager\core\exceptions.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t8"><data value='EdgeFleetError'>EdgeFleetError</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t23">edge_device_fleet_manager\core\exceptions.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t23"><data value='ConfigurationError'>ConfigurationError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t28">edge_device_fleet_manager\core\exceptions.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t28"><data value='PluginError'>PluginError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t33">edge_device_fleet_manager\core\exceptions.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t33"><data value='DeviceError'>DeviceError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t38">edge_device_fleet_manager\core\exceptions.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t38"><data value='DiscoveryError'>DiscoveryError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t43">edge_device_fleet_manager\core\exceptions.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t43"><data value='TelemetryError'>TelemetryError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t48">edge_device_fleet_manager\core\exceptions.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t48"><data value='RepositoryError'>RepositoryError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t53">edge_device_fleet_manager\core\exceptions.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t53"><data value='ValidationError'>ValidationError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t58">edge_device_fleet_manager\core\exceptions.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t58"><data value='AuthenticationError'>AuthenticationError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t63">edge_device_fleet_manager\core\exceptions.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t63"><data value='AuthorizationError'>AuthorizationError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t68">edge_device_fleet_manager\core\exceptions.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t68"><data value='NetworkError'>NetworkError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t73">edge_device_fleet_manager\core\exceptions.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t73"><data value='TimeoutError'>TimeoutError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t78">edge_device_fleet_manager\core\exceptions.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t78"><data value='RateLimitError'>RateLimitError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t83">edge_device_fleet_manager\core\exceptions.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t83"><data value='EncryptionError'>EncryptionError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t88">edge_device_fleet_manager\core\exceptions.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t88"><data value='SecretsManagerError'>SecretsManagerError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html">edge_device_fleet_manager\core\exceptions.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>31</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="31 31">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t25">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t25"><data value='CorrelationIDProcessor'>CorrelationIDProcessor</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t35">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t35"><data value='SamplingProcessor'>SamplingProcessor</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t50">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t50"><data value='TimestampProcessor'>TimestampProcessor</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t58">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t58"><data value='LevelProcessor'>LevelProcessor</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t66">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t66"><data value='ContextProcessor'>ContextProcessor</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t92">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t92"><data value='ExceptionProcessor'>ExceptionProcessor</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t110">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t110"><data value='AsyncSentryHandler'>AsyncSentryHandler</data></a></td>
                <td>38</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 38">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>61</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="43 61">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t26">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t26"><data value='PluginMetadata'>PluginMetadata</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t46">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t46"><data value='Plugin'>Plugin</data></a></td>
                <td>9</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="7 9">78%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t78">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t78"><data value='PluginLoadResult'>PluginLoadResult</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t94">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t94"><data value='PluginFileHandler'>PluginFileHandler</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t158">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t158"><data value='PluginLoader'>PluginLoader</data></a></td>
                <td>110</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="64 110">58%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>61</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="51 61">84%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_50d91cd27b284a0f___init___py.html">edge_device_fleet_manager\plugins\__init__.py</a></td>
                <td class="name left"><a href="z_50d91cd27b284a0f___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_50d91cd27b284a0f_sample_plugin_py.html#t14">edge_device_fleet_manager\plugins\sample_plugin.py</a></td>
                <td class="name left"><a href="z_50d91cd27b284a0f_sample_plugin_py.html#t14"><data value='SamplePlugin'>SamplePlugin</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_50d91cd27b284a0f_sample_plugin_py.html">edge_device_fleet_manager\plugins\sample_plugin.py</a></td>
                <td class="name left"><a href="z_50d91cd27b284a0f_sample_plugin_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4e142099aadd2379___init___py.html">edge_device_fleet_manager\utils\__init__.py</a></td>
                <td class="name left"><a href="z_4e142099aadd2379___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html">edge_device_fleet_manager\utils\decorators.py</a></td>
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>127</td>
                <td>127</td>
                <td>0</td>
                <td class="right" data-ratio="0 127">0%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>1270</td>
                <td>837</td>
                <td>2</td>
                <td class="right" data-ratio="433 1270">34%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-07-11 19:01 +0530
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>

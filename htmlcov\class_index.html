<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">37%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-07-16 17:46 +0530
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_73bda9688a204f7a___init___py.html">edge_device_fleet_manager\__init__.py</a></td>
                <td class="name left"><a href="z_73bda9688a204f7a___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c___init___py.html">edge_device_fleet_manager\cli\__init__.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html#t29">edge_device_fleet_manager\cli\main.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html#t29"><data value='AsyncGroup'>AsyncGroup</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html#t37">edge_device_fleet_manager\cli\main.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html#t37"><data value='AsyncCommand'>AsyncCommand</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html">edge_device_fleet_manager\cli\main.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_main_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>210</td>
                <td>152</td>
                <td>2</td>
                <td class="right" data-ratio="58 210">28%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t22">edge_device_fleet_manager\cli\types.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t22"><data value='CachedSchema'>CachedSchema</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t39">edge_device_fleet_manager\cli\types.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t39"><data value='DeviceIDType'>DeviceIDType</data></a></td>
                <td>79</td>
                <td>79</td>
                <td>0</td>
                <td class="right" data-ratio="0 79">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t248">edge_device_fleet_manager\cli\types.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t248"><data value='IPAddressType'>IPAddressType</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t289">edge_device_fleet_manager\cli\types.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html#t289"><data value='SubnetType'>SubnetType</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html">edge_device_fleet_manager\cli\types.py</a></td>
                <td class="name left"><a href="z_df8cb9752f6d308c_types_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t23">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t23"><data value='DatabaseConfig'>DatabaseConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t33">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t33"><data value='MQTTConfig'>MQTTConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t45">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t45"><data value='RedisConfig'>RedisConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t56">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t56"><data value='LoggingConfig'>LoggingConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t67">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t67"><data value='SecretsConfig'>SecretsConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t76">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t76"><data value='PluginConfig'>PluginConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t85">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t85"><data value='DiscoveryConfig'>DiscoveryConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t97">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t97"><data value='Config'>Config</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t123">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t123"><data value='Config'>Config.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t150">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t150"><data value='SecretsManager'>SecretsManager</data></a></td>
                <td>100</td>
                <td>100</td>
                <td>0</td>
                <td class="right" data-ratio="0 100">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t388">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html#t388"><data value='ConfigLoader'>ConfigLoader</data></a></td>
                <td>39</td>
                <td>39</td>
                <td>0</td>
                <td class="right" data-ratio="0 39">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html">edge_device_fleet_manager\core\config.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>120</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="112 120">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t30">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html#t30"><data value='AppContext'>AppContext</data></a></td>
                <td>26</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="8 26">31%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html">edge_device_fleet_manager\core\context.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_context_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>143</td>
                <td>82</td>
                <td>0</td>
                <td class="right" data-ratio="61 143">43%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t8">edge_device_fleet_manager\core\exceptions.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t8"><data value='EdgeFleetError'>EdgeFleetError</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t23">edge_device_fleet_manager\core\exceptions.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t23"><data value='ConfigurationError'>ConfigurationError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t28">edge_device_fleet_manager\core\exceptions.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t28"><data value='PluginError'>PluginError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t33">edge_device_fleet_manager\core\exceptions.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t33"><data value='DeviceError'>DeviceError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t38">edge_device_fleet_manager\core\exceptions.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t38"><data value='DiscoveryError'>DiscoveryError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t43">edge_device_fleet_manager\core\exceptions.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t43"><data value='TelemetryError'>TelemetryError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t48">edge_device_fleet_manager\core\exceptions.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t48"><data value='RepositoryError'>RepositoryError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t53">edge_device_fleet_manager\core\exceptions.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t53"><data value='ValidationError'>ValidationError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t58">edge_device_fleet_manager\core\exceptions.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t58"><data value='AuthenticationError'>AuthenticationError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t63">edge_device_fleet_manager\core\exceptions.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t63"><data value='AuthorizationError'>AuthorizationError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t68">edge_device_fleet_manager\core\exceptions.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t68"><data value='NetworkError'>NetworkError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t73">edge_device_fleet_manager\core\exceptions.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t73"><data value='TimeoutError'>TimeoutError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t78">edge_device_fleet_manager\core\exceptions.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t78"><data value='RateLimitError'>RateLimitError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t83">edge_device_fleet_manager\core\exceptions.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t83"><data value='EncryptionError'>EncryptionError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t88">edge_device_fleet_manager\core\exceptions.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html#t88"><data value='SecretsManagerError'>SecretsManagerError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html">edge_device_fleet_manager\core\exceptions.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_exceptions_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>31</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="31 31">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t25">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t25"><data value='CorrelationIDProcessor'>CorrelationIDProcessor</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t35">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t35"><data value='SamplingProcessor'>SamplingProcessor</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t50">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t50"><data value='TimestampProcessor'>TimestampProcessor</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t58">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t58"><data value='LevelProcessor'>LevelProcessor</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t66">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t66"><data value='ContextProcessor'>ContextProcessor</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t92">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t92"><data value='ExceptionProcessor'>ExceptionProcessor</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t110">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html#t110"><data value='AsyncSentryHandler'>AsyncSentryHandler</data></a></td>
                <td>38</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 38">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html">edge_device_fleet_manager\core\logging.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_logging_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>61</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="43 61">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t26">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t26"><data value='PluginMetadata'>PluginMetadata</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t46">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t46"><data value='Plugin'>Plugin</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t78">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t78"><data value='PluginLoadResult'>PluginLoadResult</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t94">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t94"><data value='PluginFileHandler'>PluginFileHandler</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t158">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html#t158"><data value='PluginLoader'>PluginLoader</data></a></td>
                <td>110</td>
                <td>110</td>
                <td>0</td>
                <td class="right" data-ratio="0 110">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html">edge_device_fleet_manager\core\plugins.py</a></td>
                <td class="name left"><a href="z_daf88cc4cd5d4988_plugins_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>61</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="51 61">84%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d___init___py.html">edge_device_fleet_manager\discovery\__init__.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t26">edge_device_fleet_manager\discovery\cache.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t26"><data value='MemoryCache'>MemoryCache</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t83">edge_device_fleet_manager\discovery\cache.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t83"><data value='RedisCache'>RedisCache</data></a></td>
                <td>40</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="0 40">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t147">edge_device_fleet_manager\discovery\cache.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html#t147"><data value='DiscoveryCache'>DiscoveryCache</data></a></td>
                <td>92</td>
                <td>92</td>
                <td>0</td>
                <td class="right" data-ratio="0 92">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html">edge_device_fleet_manager\discovery\cache.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_cache_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>43</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="41 43">95%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t22">edge_device_fleet_manager\discovery\core.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t22"><data value='DeviceType'>DeviceType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t37">edge_device_fleet_manager\discovery\core.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t37"><data value='DeviceStatus'>DeviceStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t46">edge_device_fleet_manager\discovery\core.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t46"><data value='Device'>Device</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t110">edge_device_fleet_manager\discovery\core.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t110"><data value='DiscoveryResult'>DiscoveryResult</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t129">edge_device_fleet_manager\discovery\core.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t129"><data value='DiscoveryProtocol'>DiscoveryProtocol</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>4</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t151">edge_device_fleet_manager\discovery\core.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t151"><data value='DeviceRegistry'>DeviceRegistry</data></a></td>
                <td>55</td>
                <td>55</td>
                <td>0</td>
                <td class="right" data-ratio="0 55">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t251">edge_device_fleet_manager\discovery\core.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html#t251"><data value='DiscoveryEngine'>DiscoveryEngine</data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html">edge_device_fleet_manager\discovery\core.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_core_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>77</td>
                <td>0</td>
                <td>4</td>
                <td class="right" data-ratio="77 77">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_exceptions_py.html#t10">edge_device_fleet_manager\discovery\exceptions.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_exceptions_py.html#t10"><data value='DiscoveryError'>DiscoveryError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_exceptions_py.html#t15">edge_device_fleet_manager\discovery\exceptions.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_exceptions_py.html#t15"><data value='DiscoveryTimeoutError'>DiscoveryTimeoutError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_exceptions_py.html#t20">edge_device_fleet_manager\discovery\exceptions.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_exceptions_py.html#t20"><data value='RateLimitExceededError'>RateLimitExceededError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_exceptions_py.html#t25">edge_device_fleet_manager\discovery\exceptions.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_exceptions_py.html#t25"><data value='DeviceNotFoundError'>DeviceNotFoundError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_exceptions_py.html#t30">edge_device_fleet_manager\discovery\exceptions.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_exceptions_py.html#t30"><data value='ProtocolNotAvailableError'>ProtocolNotAvailableError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_exceptions_py.html#t35">edge_device_fleet_manager\discovery\exceptions.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_exceptions_py.html#t35"><data value='NetworkError'>NetworkError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_exceptions_py.html#t40">edge_device_fleet_manager\discovery\exceptions.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_exceptions_py.html#t40"><data value='InvalidDeviceError'>InvalidDeviceError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_exceptions_py.html#t45">edge_device_fleet_manager\discovery\exceptions.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_exceptions_py.html#t45"><data value='CacheError'>CacheError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_exceptions_py.html">edge_device_fleet_manager\discovery\exceptions.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_exceptions_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e___init___py.html">edge_device_fleet_manager\discovery\protocols\__init__.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_mdns_py.html#t22">edge_device_fleet_manager\discovery\protocols\mdns.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_mdns_py.html#t22"><data value='MDNSQuery'>MDNSQuery</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_mdns_py.html#t62">edge_device_fleet_manager\discovery\protocols\mdns.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_mdns_py.html#t62"><data value='MDNSResponse'>MDNSResponse</data></a></td>
                <td>162</td>
                <td>162</td>
                <td>0</td>
                <td class="right" data-ratio="0 162">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_mdns_py.html#t348">edge_device_fleet_manager\discovery\protocols\mdns.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_mdns_py.html#t348"><data value='MDNSDiscovery'>MDNSDiscovery</data></a></td>
                <td>73</td>
                <td>73</td>
                <td>0</td>
                <td class="right" data-ratio="0 73">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_mdns_py.html">edge_device_fleet_manager\discovery\protocols\mdns.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_mdns_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>34</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="34 34">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_network_scan_py.html#t25">edge_device_fleet_manager\discovery\protocols\network_scan.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_network_scan_py.html#t25"><data value='PortScanner'>PortScanner</data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_network_scan_py.html#t119">edge_device_fleet_manager\discovery\protocols\network_scan.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_network_scan_py.html#t119"><data value='ServiceIdentifier'>ServiceIdentifier</data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_network_scan_py.html#t219">edge_device_fleet_manager\discovery\protocols\network_scan.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_network_scan_py.html#t219"><data value='NetworkDiscovery'>NetworkDiscovery</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_network_scan_py.html#t277">edge_device_fleet_manager\discovery\protocols\network_scan.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_network_scan_py.html#t277"><data value='NetworkScanDiscovery'>NetworkScanDiscovery</data></a></td>
                <td>98</td>
                <td>98</td>
                <td>0</td>
                <td class="right" data-ratio="0 98">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_network_scan_py.html">edge_device_fleet_manager\discovery\protocols\network_scan.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_network_scan_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>37</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="37 37">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_ssdp_py.html#t25">edge_device_fleet_manager\discovery\protocols\ssdp.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_ssdp_py.html#t25"><data value='SSDPMessage'>SSDPMessage</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_ssdp_py.html#t61">edge_device_fleet_manager\discovery\protocols\ssdp.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_ssdp_py.html#t61"><data value='UPnPDeviceParser'>UPnPDeviceParser</data></a></td>
                <td>44</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="0 44">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_ssdp_py.html#t142">edge_device_fleet_manager\discovery\protocols\ssdp.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_ssdp_py.html#t142"><data value='SSDPDiscovery'>SSDPDiscovery</data></a></td>
                <td>118</td>
                <td>118</td>
                <td>0</td>
                <td class="right" data-ratio="0 118">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db3d818ed52c5b9e_ssdp_py.html">edge_device_fleet_manager\discovery\protocols\ssdp.py</a></td>
                <td class="name left"><a href="z_db3d818ed52c5b9e_ssdp_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>32</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="32 32">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_rate_limiter_py.html#t21">edge_device_fleet_manager\discovery\rate_limiter.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_rate_limiter_py.html#t21"><data value='RateLimitConfig'>RateLimitConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_rate_limiter_py.html#t31">edge_device_fleet_manager\discovery\rate_limiter.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_rate_limiter_py.html#t31"><data value='TokenBucket'>TokenBucket</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_rate_limiter_py.html#t75">edge_device_fleet_manager\discovery\rate_limiter.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_rate_limiter_py.html#t75"><data value='AdaptiveRateLimiter'>AdaptiveRateLimiter</data></a></td>
                <td>45</td>
                <td>45</td>
                <td>0</td>
                <td class="right" data-ratio="0 45">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_rate_limiter_py.html#t201">edge_device_fleet_manager\discovery\rate_limiter.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_rate_limiter_py.html#t201"><data value='RateLimiter'>RateLimiter</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_93be9be7fbc09c3d_rate_limiter_py.html">edge_device_fleet_manager\discovery\rate_limiter.py</a></td>
                <td class="name left"><a href="z_93be9be7fbc09c3d_rate_limiter_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>36</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="36 36">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_50d91cd27b284a0f___init___py.html">edge_device_fleet_manager\plugins\__init__.py</a></td>
                <td class="name left"><a href="z_50d91cd27b284a0f___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_50d91cd27b284a0f_sample_plugin_py.html#t14">edge_device_fleet_manager\plugins\sample_plugin.py</a></td>
                <td class="name left"><a href="z_50d91cd27b284a0f_sample_plugin_py.html#t14"><data value='SamplePlugin'>SamplePlugin</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_50d91cd27b284a0f_sample_plugin_py.html">edge_device_fleet_manager\plugins\sample_plugin.py</a></td>
                <td class="name left"><a href="z_50d91cd27b284a0f_sample_plugin_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_17de617221a3d7e6___init___py.html">edge_device_fleet_manager\repository\__init__.py</a></td>
                <td class="name left"><a href="z_17de617221a3d7e6___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233___init___py.html">edge_device_fleet_manager\repository\application\__init__.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t18">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t18"><data value='Command'>Command</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>2</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t35">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t35"><data value='RegisterDeviceCommand'>RegisterDeviceCommand</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t59">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t59"><data value='UpdateDeviceCommand'>UpdateDeviceCommand</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t76">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t76"><data value='DeactivateDeviceCommand'>DeactivateDeviceCommand</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t90">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t90"><data value='ActivateDeviceCommand'>ActivateDeviceCommand</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t103">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t103"><data value='SetMaintenanceModeCommand'>SetMaintenanceModeCommand</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t113">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t113"><data value='UpdateLocationCommand'>UpdateLocationCommand</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t127">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t127"><data value='UpdateCapabilitiesCommand'>UpdateCapabilitiesCommand</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t141">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t141"><data value='UpdateConfigurationCommand'>UpdateConfigurationCommand</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t157">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t157"><data value='RemoveConfigurationCommand'>RemoveConfigurationCommand</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t168">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t168"><data value='RecordMetricsCommand'>RecordMetricsCommand</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t183">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t183"><data value='DeleteDeviceCommand'>DeleteDeviceCommand</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t196">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t196"><data value='BulkUpdateDevicesCommand'>BulkUpdateDevicesCommand</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t208">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t208"><data value='ImportDevicesCommand'>ImportDevicesCommand</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t221">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t221"><data value='SyncDeviceCommand'>SyncDeviceCommand</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t235">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t235"><data value='CommandValidator'>CommandValidator</data></a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t307">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html#t307"><data value='CommandResult'>CommandResult</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html">edge_device_fleet_manager\repository\application\commands.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_commands_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>102</td>
                <td>0</td>
                <td>3</td>
                <td class="right" data-ratio="102 102">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_dto_py.html#t18">edge_device_fleet_manager\repository\application\dto.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_dto_py.html#t18"><data value='DeviceIdentifierDto'>DeviceIdentifierDto</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_dto_py.html#t27">edge_device_fleet_manager\repository\application\dto.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_dto_py.html#t27"><data value='DeviceLocationDto'>DeviceLocationDto</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_dto_py.html#t50">edge_device_fleet_manager\repository\application\dto.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_dto_py.html#t50"><data value='DeviceCapabilitiesDto'>DeviceCapabilitiesDto</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_dto_py.html#t76">edge_device_fleet_manager\repository\application\dto.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_dto_py.html#t76"><data value='DeviceMetricsDto'>DeviceMetricsDto</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_dto_py.html#t96">edge_device_fleet_manager\repository\application\dto.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_dto_py.html#t96"><data value='DeviceConfigurationDto'>DeviceConfigurationDto</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_dto_py.html#t106">edge_device_fleet_manager\repository\application\dto.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_dto_py.html#t106"><data value='DeviceDto'>DeviceDto</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_dto_py.html#t228">edge_device_fleet_manager\repository\application\dto.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_dto_py.html#t228"><data value='DeviceListDto'>DeviceListDto</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_dto_py.html#t241">edge_device_fleet_manager\repository\application\dto.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_dto_py.html#t241"><data value='DeviceSearchResultDto'>DeviceSearchResultDto</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_dto_py.html#t256">edge_device_fleet_manager\repository\application\dto.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_dto_py.html#t256"><data value='DeviceStatisticsDto'>DeviceStatisticsDto</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_dto_py.html#t271">edge_device_fleet_manager\repository\application\dto.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_dto_py.html#t271"><data value='DeviceHealthDto'>DeviceHealthDto</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_dto_py.html#t284">edge_device_fleet_manager\repository\application\dto.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_dto_py.html#t284"><data value='DeviceGroupDto'>DeviceGroupDto</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_dto_py.html#t299">edge_device_fleet_manager\repository\application\dto.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_dto_py.html#t299"><data value='BulkOperationResultDto'>BulkOperationResultDto</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_dto_py.html#t311">edge_device_fleet_manager\repository\application\dto.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_dto_py.html#t311"><data value='DeviceImportResultDto'>DeviceImportResultDto</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_dto_py.html#t327">edge_device_fleet_manager\repository\application\dto.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_dto_py.html#t327"><data value='DtoConverter'>DtoConverter</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_dto_py.html">edge_device_fleet_manager\repository\application\dto.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_dto_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>154</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="154 154">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t26">edge_device_fleet_manager\repository\application\handlers.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t26"><data value='CommandHandler'>CommandHandler</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t35">edge_device_fleet_manager\repository\application\handlers.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t35"><data value='QueryHandler'>QueryHandler</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t44">edge_device_fleet_manager\repository\application\handlers.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t44"><data value='DeviceCommandHandler'>DeviceCommandHandler</data></a></td>
                <td>126</td>
                <td>126</td>
                <td>0</td>
                <td class="right" data-ratio="0 126">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t319">edge_device_fleet_manager\repository\application\handlers.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t319"><data value='DeviceQueryHandler'>DeviceQueryHandler</data></a></td>
                <td>84</td>
                <td>84</td>
                <td>0</td>
                <td class="right" data-ratio="0 84">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t525">edge_device_fleet_manager\repository\application\handlers.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t525"><data value='DeviceQueryResult'>DeviceQueryResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t531">edge_device_fleet_manager\repository\application\handlers.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t531"><data value='DeviceListQueryResult'>DeviceListQueryResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t537">edge_device_fleet_manager\repository\application\handlers.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html#t537"><data value='DeviceSearchQueryResult'>DeviceSearchQueryResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html">edge_device_fleet_manager\repository\application\handlers.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_handlers_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>42</td>
                <td>0</td>
                <td>4</td>
                <td class="right" data-ratio="42 42">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t18">edge_device_fleet_manager\repository\application\queries.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t18"><data value='Query'>Query</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>2</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t35">edge_device_fleet_manager\repository\application\queries.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t35"><data value='GetDeviceQuery'>GetDeviceQuery</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t51">edge_device_fleet_manager\repository\application\queries.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t51"><data value='GetDeviceBySerialNumberQuery'>GetDeviceBySerialNumberQuery</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t63">edge_device_fleet_manager\repository\application\queries.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t63"><data value='ListDevicesQuery'>ListDevicesQuery</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t82">edge_device_fleet_manager\repository\application\queries.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t82"><data value='SearchDevicesQuery'>SearchDevicesQuery</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t122">edge_device_fleet_manager\repository\application\queries.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t122"><data value='GetDevicesByTypeQuery'>GetDevicesByTypeQuery</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t136">edge_device_fleet_manager\repository\application\queries.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t136"><data value='GetDevicesByStatusQuery'>GetDevicesByStatusQuery</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t150">edge_device_fleet_manager\repository\application\queries.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t150"><data value='GetStaleDevicesQuery'>GetStaleDevicesQuery</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t164">edge_device_fleet_manager\repository\application\queries.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t164"><data value='GetDeviceMetricsQuery'>GetDeviceMetricsQuery</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t179">edge_device_fleet_manager\repository\application\queries.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t179"><data value='GetDeviceConfigurationQuery'>GetDeviceConfigurationQuery</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t191">edge_device_fleet_manager\repository\application\queries.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t191"><data value='GetDevicesWithCapabilitiesQuery'>GetDevicesWithCapabilitiesQuery</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t208">edge_device_fleet_manager\repository\application\queries.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t208"><data value='GetDevicesByLocationQuery'>GetDevicesByLocationQuery</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t227">edge_device_fleet_manager\repository\application\queries.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t227"><data value='GetDeviceStatisticsQuery'>GetDeviceStatisticsQuery</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t239">edge_device_fleet_manager\repository\application\queries.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t239"><data value='GetDeviceHealthQuery'>GetDeviceHealthQuery</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t254">edge_device_fleet_manager\repository\application\queries.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t254"><data value='QueryValidator'>QueryValidator</data></a></td>
                <td>36</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="0 36">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t332">edge_device_fleet_manager\repository\application\queries.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t332"><data value='QueryResult'>QueryResult</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t357">edge_device_fleet_manager\repository\application\queries.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html#t357"><data value='PaginatedQueryResult'>PaginatedQueryResult</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html">edge_device_fleet_manager\repository\application\queries.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_queries_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>133</td>
                <td>0</td>
                <td>3</td>
                <td class="right" data-ratio="133 133">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_services_py.html#t28">edge_device_fleet_manager\repository\application\services.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_services_py.html#t28"><data value='DeviceApplicationService'>DeviceApplicationService</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_services_py.html#t315">edge_device_fleet_manager\repository\application\services.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_services_py.html#t315"><data value='DeviceQueryService'>DeviceQueryService</data></a></td>
                <td>41</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="0 41">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8715c4b7d8e78233_services_py.html">edge_device_fleet_manager\repository\application\services.py</a></td>
                <td class="name left"><a href="z_8715c4b7d8e78233_services_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>31</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="31 31">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79___init___py.html">edge_device_fleet_manager\repository\domain\__init__.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t25">edge_device_fleet_manager\repository\domain\entities.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t25"><data value='DeviceStatus'>DeviceStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t33">edge_device_fleet_manager\repository\domain\entities.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t33"><data value='DeviceType'>DeviceType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t47">edge_device_fleet_manager\repository\domain\entities.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t47"><data value='DeviceConfiguration'>DeviceConfiguration</data></a></td>
                <td>12</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="6 12">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t95">edge_device_fleet_manager\repository\domain\entities.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t95"><data value='DeviceEntity'>DeviceEntity</data></a></td>
                <td>49</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="27 49">55%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t244">edge_device_fleet_manager\repository\domain\entities.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t244"><data value='DeviceGroup'>DeviceGroup</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t304">edge_device_fleet_manager\repository\domain\entities.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html#t304"><data value='DeviceAggregate'>DeviceAggregate</data></a></td>
                <td>38</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="23 38">61%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html">edge_device_fleet_manager\repository\domain\entities.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_entities_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>100</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="100 100">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t17">edge_device_fleet_manager\repository\domain\events.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t17"><data value='DomainEvent'>DomainEvent</data></a></td>
                <td>5</td>
                <td>1</td>
                <td>4</td>
                <td class="right" data-ratio="4 5">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t50">edge_device_fleet_manager\repository\domain\events.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t50"><data value='DeviceRegisteredEvent'>DeviceRegisteredEvent</data></a></td>
                <td>15</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="9 15">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t112">edge_device_fleet_manager\repository\domain\events.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t112"><data value='DeviceUpdatedEvent'>DeviceUpdatedEvent</data></a></td>
                <td>5</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="4 5">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t132">edge_device_fleet_manager\repository\domain\events.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t132"><data value='DeviceDeactivatedEvent'>DeviceDeactivatedEvent</data></a></td>
                <td>5</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="4 5">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t152">edge_device_fleet_manager\repository\domain\events.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t152"><data value='DeviceActivatedEvent'>DeviceActivatedEvent</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t169">edge_device_fleet_manager\repository\domain\events.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t169"><data value='DeviceConfigurationChangedEvent'>DeviceConfigurationChangedEvent</data></a></td>
                <td>7</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="5 7">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t193">edge_device_fleet_manager\repository\domain\events.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t193"><data value='DeviceMetricsRecordedEvent'>DeviceMetricsRecordedEvent</data></a></td>
                <td>4</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="3 4">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t211">edge_device_fleet_manager\repository\domain\events.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t211"><data value='DeviceLocationChangedEvent'>DeviceLocationChangedEvent</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t249">edge_device_fleet_manager\repository\domain\events.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html#t249"><data value='DeviceCapabilitiesUpdatedEvent'>DeviceCapabilitiesUpdatedEvent</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html">edge_device_fleet_manager\repository\domain\events.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_events_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>53</td>
                <td>0</td>
                <td>5</td>
                <td class="right" data-ratio="53 53">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_services_py.html#t17">edge_device_fleet_manager\repository\domain\services.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_services_py.html#t17"><data value='DeviceValidationService'>DeviceValidationService</data></a></td>
                <td>35</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="22 35">63%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_services_py.html#t83">edge_device_fleet_manager\repository\domain\services.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_services_py.html#t83"><data value='DeviceRegistrationService'>DeviceRegistrationService</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_services_py.html#t154">edge_device_fleet_manager\repository\domain\services.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_services_py.html#t154"><data value='DeviceLifecycleService'>DeviceLifecycleService</data></a></td>
                <td>68</td>
                <td>50</td>
                <td>0</td>
                <td class="right" data-ratio="18 68">26%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_services_py.html">edge_device_fleet_manager\repository\domain\services.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_services_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>30</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="30 30">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t19">edge_device_fleet_manager\repository\domain\value_objects.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t19"><data value='DeviceId'>DeviceId</data></a></td>
                <td>8</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="7 8">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t46">edge_device_fleet_manager\repository\domain\value_objects.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t46"><data value='DeviceIdentifier'>DeviceIdentifier</data></a></td>
                <td>14</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="7 14">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t79">edge_device_fleet_manager\repository\domain\value_objects.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t79"><data value='DeviceLocation'>DeviceLocation</data></a></td>
                <td>25</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="9 25">36%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t135">edge_device_fleet_manager\repository\domain\value_objects.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t135"><data value='DeviceCapabilities'>DeviceCapabilities</data></a></td>
                <td>25</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="12 25">48%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t207">edge_device_fleet_manager\repository\domain\value_objects.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html#t207"><data value='DeviceMetrics'>DeviceMetrics</data></a></td>
                <td>29</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="16 29">55%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html">edge_device_fleet_manager\repository\domain\value_objects.py</a></td>
                <td class="name left"><a href="z_b8bec98587f6fb79_value_objects_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>83</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="83 83">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7___init___py.html">edge_device_fleet_manager\repository\infrastructure\__init__.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_database_py.html#t26">edge_device_fleet_manager\repository\infrastructure\database.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_database_py.html#t26"><data value='DatabaseSession'>DatabaseSession</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_database_py.html#t137">edge_device_fleet_manager\repository\infrastructure\database.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_database_py.html#t137"><data value='DatabaseMigration'>DatabaseMigration</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_database_py.html">edge_device_fleet_manager\repository\infrastructure\database.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_database_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>94</td>
                <td>64</td>
                <td>0</td>
                <td class="right" data-ratio="30 94">32%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html#t24">edge_device_fleet_manager\repository\infrastructure\event_store.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html#t24"><data value='EventStoreError'>EventStoreError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html#t32">edge_device_fleet_manager\repository\infrastructure\event_store.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html#t32"><data value='StoredEvent'>StoredEvent</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html#t50">edge_device_fleet_manager\repository\infrastructure\event_store.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html#t50"><data value='EventStore'>EventStore</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>8</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html#t74">edge_device_fleet_manager\repository\infrastructure\event_store.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html#t74"><data value='InMemoryEventStore'>InMemoryEventStore</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html#t132">edge_device_fleet_manager\repository\infrastructure\event_store.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html#t132"><data value='SqlEventStore'>SqlEventStore</data></a></td>
                <td>61</td>
                <td>61</td>
                <td>0</td>
                <td class="right" data-ratio="0 61">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html">edge_device_fleet_manager\repository\infrastructure\event_store.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_event_store_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>41</td>
                <td>0</td>
                <td>9</td>
                <td class="right" data-ratio="41 41">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t26">edge_device_fleet_manager\repository\infrastructure\repositories.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t26"><data value='DeviceModel'>DeviceModel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t71">edge_device_fleet_manager\repository\infrastructure\repositories.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t71"><data value='DeviceGroupModel'>DeviceGroupModel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t90">edge_device_fleet_manager\repository\infrastructure\repositories.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t90"><data value='DeviceRepository'>DeviceRepository</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>12</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t124">edge_device_fleet_manager\repository\infrastructure\repositories.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t124"><data value='DeviceGroupRepository'>DeviceGroupRepository</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>8</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t148">edge_device_fleet_manager\repository\infrastructure\repositories.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t148"><data value='InMemoryDeviceRepository'>InMemoryDeviceRepository</data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t214">edge_device_fleet_manager\repository\infrastructure\repositories.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html#t214"><data value='SqlDeviceRepository'>SqlDeviceRepository</data></a></td>
                <td>111</td>
                <td>111</td>
                <td>0</td>
                <td class="right" data-ratio="0 111">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html">edge_device_fleet_manager\repository\infrastructure\repositories.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_repositories_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>70</td>
                <td>0</td>
                <td>22</td>
                <td class="right" data-ratio="70 70">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t22">edge_device_fleet_manager\repository\infrastructure\unit_of_work.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t22"><data value='UnitOfWork'>UnitOfWork</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>10</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t54">edge_device_fleet_manager\repository\infrastructure\unit_of_work.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t54"><data value='SqlUnitOfWork'>SqlUnitOfWork</data></a></td>
                <td>38</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 38">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t140">edge_device_fleet_manager\repository\infrastructure\unit_of_work.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t140"><data value='InMemoryUnitOfWork'>InMemoryUnitOfWork</data></a></td>
                <td>40</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="0 40">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t240">edge_device_fleet_manager\repository\infrastructure\unit_of_work.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t240"><data value='UnitOfWorkFactory'>UnitOfWorkFactory</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t260">edge_device_fleet_manager\repository\infrastructure\unit_of_work.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t260"><data value='EventHandler'>EventHandler</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t269">edge_device_fleet_manager\repository\infrastructure\unit_of_work.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t269"><data value='EventDispatcher'>EventDispatcher</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t300">edge_device_fleet_manager\repository\infrastructure\unit_of_work.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t300"><data value='DeviceRegisteredEventHandler'>DeviceRegisteredEventHandler</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t309">edge_device_fleet_manager\repository\infrastructure\unit_of_work.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html#t309"><data value='DeviceMetricsRecordedEventHandler'>DeviceMetricsRecordedEventHandler</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html">edge_device_fleet_manager\repository\infrastructure\unit_of_work.py</a></td>
                <td class="name left"><a href="z_8a0001c30d6026d7_unit_of_work_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>54</td>
                <td>5</td>
                <td>12</td>
                <td class="right" data-ratio="49 54">91%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4e142099aadd2379___init___py.html">edge_device_fleet_manager\utils\__init__.py</a></td>
                <td class="name left"><a href="z_4e142099aadd2379___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html">edge_device_fleet_manager\utils\decorators.py</a></td>
                <td class="name left"><a href="z_4e142099aadd2379_decorators_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>127</td>
                <td>127</td>
                <td>0</td>
                <td class="right" data-ratio="0 127">0%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>4811</td>
                <td>3038</td>
                <td>123</td>
                <td class="right" data-ratio="1773 4811">37%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-07-16 17:46 +0530
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>

{"tests/unit/test_plugins.py::TestPluginSystem::test_plugin_base_class": true, "tests/unit/test_plugins.py::TestPluginSystem::test_plugin_timeout_handling": true, "tests/unit/test_plugins.py::TestPluginSystem::test_plugin_commands_registration": true, "tests/unit/test_config.py::TestSecretsManager::test_secrets_manager_initialization": true, "tests/unit/test_config.py::TestSecretsManager::test_get_encryption_key_new": true, "tests/unit/test_config.py::TestSecretsManager::test_get_encryption_key_existing": true, "tests/unit/test_config.py::TestSecretsManager::test_get_secret": true, "tests/unit/test_config.py::TestSecretsManager::test_set_secret": true, "tests/unit/test_config.py::TestSecretsManager::test_check_rotation_needed": true, "tests/unit/test_config.py::TestConfigLoader::test_load_config_with_secrets": true, "tests/unit/test_config.py::TestConfigLoader::test_config_loading_error_handling": true, "tests/unit/test_config.py::TestGlobalConfigFunctions::test_get_config_reload": true}
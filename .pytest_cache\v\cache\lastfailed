{"tests/unit/test_config.py::TestSecretsManager::test_secrets_manager_initialization": true, "tests/unit/test_config.py::TestSecretsManager::test_get_encryption_key_new": true, "tests/unit/test_config.py::TestSecretsManager::test_get_encryption_key_existing": true, "tests/unit/test_config.py::TestSecretsManager::test_get_secret": true, "tests/unit/test_config.py::TestSecretsManager::test_set_secret": true, "tests/unit/test_config.py::TestSecretsManager::test_check_rotation_needed": true, "tests/unit/test_config.py::TestConfigLoader::test_load_config_with_secrets": true, "tests/unit/test_config.py::TestConfigLoader::test_config_loading_error_handling": true, "tests/unit/test_config.py::TestGlobalConfigFunctions::test_get_config_reload": true, "tests/unit/test_discovery_rate_limiter.py::TestTokenBucket::test_consume_tokens": true, "tests/unit/test_discovery_rate_limiter.py::TestAdaptiveRateLimiter::test_acquire_global_limit": true, "tests/unit/test_discovery_rate_limiter.py::TestAdaptiveRateLimiter::test_acquire_per_host_limit": true, "tests/unit/test_discovery_rate_limiter.py::TestAdaptiveRateLimiter::test_get_global_stats": true, "tests/unit/test_discovery_cache.py::TestDiscoveryCache::test_remove_device": true, "tests/unit/test_discovery_cache.py::TestDiscoveryCache::test_dict_to_device_conversion": true, "tests/unit/test_repository_infrastructure.py::TestDatabaseSession::test_create_test_database": true, "tests/unit/test_repository_infrastructure.py::TestDatabaseSession::test_database_migration": true, "tests/unit/test_repository_infrastructure.py::TestDatabaseSession::test_session_context_manager": true, "tests/unit/test_repository_infrastructure.py::TestDatabaseSession::test_create_and_drop_tables": true, "tests/unit/test_repository_application.py::TestCommandHandler::test_handle_update_device_command": true, "tests/unit/test_repository_application.py::TestQueryHandler::test_handle_get_device_query": true, "tests/unit/test_repository_application.py::TestQueryHandler::test_handle_get_nonexistent_device": true, "tests/unit/test_repository_application.py::TestQueryHandler::test_handle_list_devices_query": true, "tests/unit/test_repository_application.py::TestApplicationService::test_register_device": true, "tests/unit/test_repository_application.py::TestApplicationService::test_get_device": true}
"""
Unit tests for the plugin system with hot-reload capability.

This test specifically addresses the requirement:
"Using pytest-<PERSON><PERSON><PERSON> and <PERSON><PERSON>'s <PERSON><PERSON><PERSON><PERSON><PERSON>, simulate a plugin load error 
(syntax exception) and assert the CLI logs a warning yet continues loading others."
"""

import asyncio
import tempfile
from pathlib import Path
from unittest.mock import MagicMock, patch

import pytest
import pytest_asyncio
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

from edge_device_fleet_manager.core.plugins import (
    <PERSON>lugin,
    PluginLoader,
    PluginMetadata,
    initialize_plugin_system,
)
from edge_device_fleet_manager.core.config import PluginConfig
from edge_device_fleet_manager.core.exceptions import PluginError
from edge_device_fleet_manager.cli.main import cli


class TestPlugin(Plugin):
    """Test plugin for unit tests."""
    
    metadata = PluginMetadata(
        name="test_plugin",
        version="1.0.0",
        description="Test plugin for unit tests",
        author="Test Author"
    )
    
    def __init__(self):
        super().__init__()
        self.initialized = False
        self.cleaned_up = False
    
    def initialize(self, config):
        """Initialize the plugin."""
        self.initialized = True
    
    def cleanup(self):
        """Cleanup the plugin."""
        self.cleaned_up = True


@pytest.mark.unit
@pytest.mark.asyncio
class TestPluginSystem:
    """Test cases for the plugin system."""
    
    async def test_plugin_metadata(self):
        """Test plugin metadata creation."""
        metadata = PluginMetadata(
            name="test",
            version="2.0.0",
            description="Test plugin",
            author="Test Author",
            dependencies=["dep1", "dep2"],
            commands=["cmd1", "cmd2"]
        )
        
        assert metadata.name == "test"
        assert metadata.version == "2.0.0"
        assert metadata.description == "Test plugin"
        assert metadata.author == "Test Author"
        assert metadata.dependencies == ["dep1", "dep2"]
        assert metadata.commands == ["cmd1", "cmd2"]
    
    async def test_plugin_base_class(self, test_config):
        """Test the base Plugin class."""
        plugin = TestPlugin()
        
        assert plugin.metadata.name == "test_plugin"
        assert plugin.metadata.version == "1.0.0"
        assert not plugin.initialized
        assert not plugin.cleaned_up
        
        # Test initialization
        plugin.initialize(test_config)
        assert plugin.initialized
        
        # Test cleanup
        plugin.cleanup()
        assert plugin.cleaned_up
    
    async def test_plugin_loader_initialization(self, temp_dir):
        """Test plugin loader initialization."""
        plugins_dir = temp_dir / "plugins"
        plugins_dir.mkdir()
        
        config = PluginConfig(
            plugins_dir=str(plugins_dir),
            auto_reload=False,
            reload_delay=1.0,
            max_load_retries=3,
            load_timeout=30
        )
        
        loader = PluginLoader(config)
        
        assert loader.config == config
        assert loader.plugins_dir == plugins_dir
        assert len(loader.loaded_plugins) == 0
        assert len(loader.plugin_modules) == 0
        assert len(loader.plugin_files) == 0
        assert loader.observer is None
        assert loader.cli_group is None
    
    async def test_successful_plugin_loading(self, temp_dir, sample_plugin_code):
        """Test successful plugin loading."""
        plugins_dir = temp_dir / "plugins"
        plugins_dir.mkdir()
        
        # Create a valid plugin file
        plugin_file = plugins_dir / "test_plugin.py"
        plugin_file.write_text(sample_plugin_code)
        
        config = PluginConfig(
            plugins_dir=str(plugins_dir),
            auto_reload=False
        )
        
        loader = PluginLoader(config)
        await loader.start()
        
        try:
            # Check that plugin was loaded
            assert len(loader.loaded_plugins) == 1
            assert "test_plugin" in loader.loaded_plugins
            
            plugin = loader.get_plugin("test_plugin")
            assert plugin is not None
            assert plugin.metadata.name == "test"
            
        finally:
            await loader.stop()
    
    async def test_plugin_load_error_continues_loading_others(
        self, temp_dir, sample_plugin_code, broken_plugin_code, caplog
    ):
        """
        Test that plugin load errors are logged as warnings but don't stop 
        loading other plugins.
        
        This addresses the specific requirement from the prompt.
        """
        plugins_dir = temp_dir / "plugins"
        plugins_dir.mkdir()
        
        # Create a valid plugin file
        valid_plugin_file = plugins_dir / "valid_plugin.py"
        valid_plugin_file.write_text(sample_plugin_code)
        
        # Create a broken plugin file
        broken_plugin_file = plugins_dir / "broken_plugin.py"
        broken_plugin_file.write_text(broken_plugin_code)
        
        # Create another valid plugin file
        another_valid_plugin = plugins_dir / "another_valid.py"
        another_valid_plugin.write_text(sample_plugin_code.replace("TestPlugin", "AnotherTestPlugin"))
        
        config = PluginConfig(
            plugins_dir=str(plugins_dir),
            auto_reload=False
        )
        
        loader = PluginLoader(config)
        
        # Clear any previous log records
        caplog.clear()
        
        # Load all plugins
        results = await loader.load_all_plugins()
        
        try:
            # Check that we attempted to load 3 plugins
            assert len(results) == 3
            
            # Check that 2 succeeded and 1 failed
            successful_loads = [r for r in results if r.success]
            failed_loads = [r for r in results if not r.success]
            
            assert len(successful_loads) == 2
            assert len(failed_loads) == 1
            
            # Check that the valid plugins were loaded
            assert len(loader.loaded_plugins) == 2
            
            # Check that error was logged
            error_logs = [record for record in caplog.records if record.levelname == "ERROR"]
            assert len(error_logs) >= 1
            
            # Verify the error message mentions plugin loading failure
            error_messages = [record.message for record in error_logs]
            assert any("Failed to load plugin" in msg for msg in error_messages)
            
        finally:
            await loader.stop()
    
    async def test_plugin_unloading(self, temp_dir, sample_plugin_code):
        """Test plugin unloading."""
        plugins_dir = temp_dir / "plugins"
        plugins_dir.mkdir()
        
        plugin_file = plugins_dir / "test_plugin.py"
        plugin_file.write_text(sample_plugin_code)
        
        config = PluginConfig(
            plugins_dir=str(plugins_dir),
            auto_reload=False
        )
        
        loader = PluginLoader(config)
        await loader.start()
        
        try:
            # Verify plugin is loaded
            assert len(loader.loaded_plugins) == 1
            assert "test_plugin" in loader.loaded_plugins
            
            # Unload the plugin
            result = await loader.unload_plugin("test_plugin")
            assert result is True
            
            # Verify plugin is unloaded
            assert len(loader.loaded_plugins) == 0
            assert "test_plugin" not in loader.loaded_plugins
            
        finally:
            await loader.stop()
    
    async def test_plugin_reload(self, temp_dir, sample_plugin_code):
        """Test plugin reloading."""
        plugins_dir = temp_dir / "plugins"
        plugins_dir.mkdir()
        
        plugin_file = plugins_dir / "test_plugin.py"
        plugin_file.write_text(sample_plugin_code)
        
        config = PluginConfig(
            plugins_dir=str(plugins_dir),
            auto_reload=False
        )
        
        loader = PluginLoader(config)
        await loader.start()
        
        try:
            # Verify initial load
            assert len(loader.loaded_plugins) == 1
            original_plugin = loader.get_plugin("test_plugin")
            
            # Reload the plugin
            result = await loader.reload_plugin_from_file(str(plugin_file))
            assert result.success
            
            # Verify plugin is still loaded but is a new instance
            assert len(loader.loaded_plugins) == 1
            reloaded_plugin = loader.get_plugin("test_plugin")
            assert reloaded_plugin is not original_plugin
            
        finally:
            await loader.stop()
    
    @patch('edge_device_fleet_manager.core.plugins.Observer')
    async def test_file_watcher_start_stop(self, mock_observer_class, temp_dir):
        """Test file watcher start and stop."""
        mock_observer = MagicMock()
        mock_observer_class.return_value = mock_observer
        
        plugins_dir = temp_dir / "plugins"
        plugins_dir.mkdir()
        
        config = PluginConfig(
            plugins_dir=str(plugins_dir),
            auto_reload=True
        )
        
        loader = PluginLoader(config)
        
        # Start the loader (should start file watcher)
        await loader.start()
        
        # Verify observer was created and started
        mock_observer_class.assert_called_once()
        mock_observer.schedule.assert_called_once()
        mock_observer.start.assert_called_once()
        
        # Stop the loader (should stop file watcher)
        await loader.stop()
        
        # Verify observer was stopped
        mock_observer.stop.assert_called_once()
        mock_observer.join.assert_called_once()
    
    async def test_plugin_timeout_handling(self, temp_dir):
        """Test plugin loading timeout handling."""
        plugins_dir = temp_dir / "plugins"
        plugins_dir.mkdir()
        
        # Create a plugin that takes too long to load
        slow_plugin_code = '''
import time
import click
from edge_device_fleet_manager.core.plugins import Plugin, PluginMetadata

class SlowPlugin(Plugin):
    metadata = PluginMetadata(name="slow", version="1.0.0")
    
    def __init__(self):
        super().__init__()
        time.sleep(2)  # Simulate slow loading
'''
        
        plugin_file = plugins_dir / "slow_plugin.py"
        plugin_file.write_text(slow_plugin_code)
        
        config = PluginConfig(
            plugins_dir=str(plugins_dir),
            auto_reload=False,
            load_timeout=1  # 1 second timeout
        )
        
        loader = PluginLoader(config)
        
        # Load plugin with timeout
        result = await loader.load_plugin_from_file(str(plugin_file))
        
        # Should fail due to timeout
        assert not result.success
        assert result.error is not None
        assert isinstance(result.error, asyncio.TimeoutError)
    
    async def test_cli_integration_with_plugins(self, cli_runner, temp_dir, sample_plugin_code):
        """Test CLI integration with plugin system."""
        plugins_dir = temp_dir / "plugins"
        plugins_dir.mkdir()
        
        plugin_file = plugins_dir / "test_plugin.py"
        plugin_file.write_text(sample_plugin_code)
        
        # Test CLI with plugins
        with patch.dict('os.environ', {'PLUGINS__PLUGINS_DIR': str(plugins_dir)}):
            result = cli_runner.invoke(cli, ['plugins'])
            
            # Should not crash and should show plugin information
            assert result.exit_code == 0
    
    async def test_plugin_commands_registration(self, temp_dir):
        """Test that plugin commands are properly registered with CLI."""
        plugins_dir = temp_dir / "plugins"
        plugins_dir.mkdir()
        
        plugin_with_commands = '''
import click
from edge_device_fleet_manager.core.plugins import Plugin, PluginMetadata

class CommandPlugin(Plugin):
    metadata = PluginMetadata(
        name="command_plugin",
        version="1.0.0",
        commands=["hello", "goodbye"]
    )
    
    @click.command()
    def hello(self):
        """Say hello."""
        click.echo("Hello from plugin!")
    
    @click.command()
    def goodbye(self):
        """Say goodbye."""
        click.echo("Goodbye from plugin!")
'''
        
        plugin_file = plugins_dir / "command_plugin.py"
        plugin_file.write_text(plugin_with_commands)
        
        config = PluginConfig(
            plugins_dir=str(plugins_dir),
            auto_reload=False
        )
        
        # Create a mock CLI group
        mock_cli_group = MagicMock()
        
        loader = PluginLoader(config)
        loader.set_cli_group(mock_cli_group)
        
        await loader.start()
        
        try:
            # Verify plugin was loaded
            assert len(loader.loaded_plugins) == 1
            
            plugin = loader.get_plugin("command_plugin")
            assert plugin is not None
            
            # Verify commands were registered
            commands = plugin.get_commands()
            assert len(commands) == 2
            
            # Verify CLI group add_command was called
            assert mock_cli_group.add_command.call_count == 2
            
        finally:
            await loader.stop()
